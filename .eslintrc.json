{
  "env": {
    "browser": true,
    "es2020": true
  },
  "extends": [
    "airbnb",
    "plugin:react/recommended",
    "plugin:import/recommended",
    "plugin:import/typescript",
    "airbnb-typescript"
  ],
  // Specifying Parser
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "project": [
      "./tsconfig.json"
    ],
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": "latest",
    "sourceType": "module",
    "tsconfigRootDir": "."
  },
  // Configuring third-party plugins
  "plugins": [
    "eslint-plugin-import",
    "@typescript-eslint",
    "react",
    "react-hooks"
  ],
  "settings": {
    "import/resolver": {
      "typescript": {
        "alwaysTryTypes": true,
        "project": "tsconfig.json"
      }
    }
  },
  "ignorePatterns": [
    "vite.config.ts",
    "public/js/config.js",
    "src/tests/setup.ts"
  ],
  "rules": {
    "jsx-a11y/control-has-associated-label": "off",
    "@typescript-eslint/semi": [
      "error",
      "never"
    ],
    "max-len": [
      "error",
      {
        "code": 120
      }
    ],
    "import/no-absolute-path": "off",
    "import/no-named-as-default": "off",
    "linebreak-style": "off",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/strict-boolean-expressions": "off",
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/ban-types": [
      "error",
      {
        "extendDefaults": true,
        "types": {
          "{}": false
        }
      }
    ],
    "import/prefer-default-export": "off",
    "import/no-named-default": "off",
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal"
        ],
        "pathGroups": [
          {
            "pattern": "react",
            "group": "external",
            "position": "before"
          }
        ],
        "pathGroupsExcludedImportTypes": [
          "react"
        ],
        "newlines-between": "always",
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ],
    "react-hooks/exhaustive-deps": "off",
    // Enforce the use of the shorthand syntax.
    "object-shorthand": "error",
    "no-console": [
      "warn",
      {
        "allow": [
          "error"
        ]
      }
    ],
    "react/react-in-jsx-scope": "off",
    "react/jsx-one-expression-per-line": "off",
    "react/require-default-props": "off",
    "react/jsx-props-no-spreading": "off",
    "react/jsx-uses-react": 0,
    "no-plusplus": [
      "error",
      {
        "allowForLoopAfterthoughts": true
      }
    ],
    "no-param-reassign": "off",
    "no-alert": "off",
    "arrow-body-style": "off"
  }
}