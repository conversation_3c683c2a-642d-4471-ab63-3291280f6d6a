{"name": "activate-frontend", "private": true, "version": "0.1.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --port 3000", "build:preview": "npm run build && npm run preview", "test": "vitest", "test:ui": "vitest --ui", "coverage": "vitest run --coverage"}, "dependencies": {"@ant-design/icons": "^5.6.0", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@reduxjs/toolkit": "^2.5.1", "antd": "^5.26.1", "i18next": "^23.6.0", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "rc-virtual-list": "^3.18.1", "react": "~18.2.0", "react-dom": "~18.2.0", "react-i18next": "^13.3.1", "react-redux": "^9.2.0", "react-router": "^7.6.2", "react-transition-group": "^4.4.5", "react-use-websocket": "^4.13.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "use-debounce": "^9.0.4", "vite-plugin-svgr": "^3.2.0", "vite-tsconfig-paths": "^4.2.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/eslint-parser": "^7.22.9", "@babel/preset-react": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.1", "@types/lodash-es": "^4.17.12", "@types/node": "^20.4.2", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/react-transition-group": "^4.4.12", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.61.0", "@vitejs/plugin-react": "^4.0.1", "@vitest/coverage-v8": "^0.34.6", "@vitest/ui": "^0.34.6", "cross-fetch": "^4.0.0", "eslint": "^8.46.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.5.5", "eslint-import-resolver-vite": "^1.3.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^16.0.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "jsdom": "^22.1.0", "msw": "^1.3.2", "redux-mock-store": "^1.5.4", "typescript": "~5.1.6", "vite": "~4.4.0", "vite-plugin-eslint": "^1.8.1", "vitest": "^0.34.6"}}