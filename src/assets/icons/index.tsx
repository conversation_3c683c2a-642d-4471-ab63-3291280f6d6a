import { ReactComponent as AlertCircleLg } from './alert-circle-lg.svg'
import { ReactComponent as AlertCircleRed } from './alert-circle-red.svg'
import { ReactComponent as AlertCircle } from './alert-circle.svg'
import { ReactComponent as AlertInfoCircle } from './alert-info-circle.svg'
import { ReactComponent as AlertTriangle } from './alert-triangle.svg'
import { ReactComponent as BellIcon } from './bell.svg'
import { ReactComponent as CheckCircleIcon } from './check-circle.svg'
import { ReactComponent as DownIcon } from './chevron-down.svg'
import { ReactComponent as RightIcon } from './chevron-right.svg'
import { ReactComponent as LeftIcon } from './chevrons-left.svg'
import { ReactComponent as DeleteIcon } from './delete.svg'
import { ReactComponent as DoubleLeftIcon } from './double-left.svg'
import { ReactComponent as DoubleRightIcon } from './double-right.svg'
import { ReactComponent as DownloadIcon } from './download.svg'
import { ReactComponent as EditIcon } from './edit.svg'
import { ReactComponent as EmptyIcon } from './emptyIcon.svg'
import { ReactComponent as EyeOffIcon } from './eye-off.svg'
import { ReactComponent as EyeIcon } from './eye.svg'
import { ReactComponent as FilterIcon } from './filter.svg'
import { ReactComponent as HistoryIcon } from './history.svg'
import { ReactComponent as LoadingIcon2 } from './load.svg'
import { ReactComponent as LoadingIcon } from './loading.svg'
import { ReactComponent as Manufacturing } from './manufacturing.svg'
import { ReactComponent as MarkIcon } from './mark.svg'
import { ReactComponent as Nevi1Icon } from './nevi-1.svg'
import { ReactComponent as NeviIcon } from './nevi.svg'
import { ReactComponent as OkCircleBlueIcon } from './ok-circle-blue.svg'
import { ReactComponent as OkCircleIcon } from './ok-circle.svg'
import { ReactComponent as OperationsCombine } from './operations_combine.svg'
import { ReactComponent as OperationsCrop } from './operations_crop.svg'
import { ReactComponent as OperationsEmpty } from './operations_empty.svg'
import { ReactComponent as OperationsMargin } from './operations_margin.svg'
import { ReactComponent as OperationsOverlay } from './operations_overlay.svg'
import { ReactComponent as OrderIcon } from './order.svg'
import { ReactComponent as PenToolIcon } from './pen-tool.svg'
import { ReactComponent as PlayIcon } from './play.svg'
import { ReactComponent as PriorityIcon } from './priority.svg'
import { ReactComponent as QuestionCircleOutlined } from './QuestionCircleOutlined.svg'
import { ReactComponent as ResendIcon } from './resend.svg'
import { ReactComponent as SearchIcon } from './search.svg'
import { ReactComponent as Setting2 } from './setting-2.svg'
import { ReactComponent as Setting3 } from './setting-3.svg'
import { ReactComponent as Setting4 } from './setting-4.svg'
import { ReactComponent as SettingIcon } from './setting.svg'
import { ReactComponent as Setting1 } from './setting1.svg'
import { ReactComponent as Settings } from './settings.svg'
import { ReactComponent as StopIcon } from './stop.svg'
import { ReactComponent as SucceededIcon } from './succeeded.svg'
import { ReactComponent as SuccessCircle } from './success-circle.svg'
import { ReactComponent as UserIcon } from './user.svg'
import { ReactComponent as WorklistIcon } from './worklist.svg'
import { ReactComponent as XCircleIcon } from './x-circle.svg'
import { ReactComponent as XLgIcon } from './X-lg.svg'
import { ReactComponent as XSmIcon } from './X-sm.svg'

export {
  AlertCircleRed,
  AlertCircle,
  AlertCircleLg,
  AlertInfoCircle,
  AlertTriangle,
  BellIcon,
  CheckCircleIcon,
  DeleteIcon,
  DoubleLeftIcon,
  DoubleRightIcon,
  DownIcon,
  LeftIcon,
  RightIcon,
  XLgIcon,
  XSmIcon,
  XCircleIcon,
  DownloadIcon,
  EditIcon,
  EyeOffIcon,
  EmptyIcon,
  EyeIcon,
  HistoryIcon,
  LoadingIcon,
  LoadingIcon2,
  MarkIcon,
  Nevi1Icon,
  NeviIcon,
  OkCircleBlueIcon,
  OkCircleIcon,
  OperationsEmpty,
  OperationsCombine,
  OperationsMargin,
  OperationsCrop,
  OperationsOverlay,
  OrderIcon,
  PenToolIcon,
  PlayIcon,
  PriorityIcon,
  SearchIcon,
  SuccessCircle,
  SettingIcon,
  Setting1,
  Setting2,
  Setting3,
  Setting4,
  Settings,
  StopIcon,
  SucceededIcon,
  Manufacturing,
  UserIcon,
  WorklistIcon,
  ResendIcon,
  FilterIcon,
  QuestionCircleOutlined,
}
