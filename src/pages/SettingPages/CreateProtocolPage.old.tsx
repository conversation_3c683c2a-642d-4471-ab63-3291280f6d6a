import { useState, useEffect } from 'react'

import { Button, Space, TabsProps } from 'antd'
import { Content } from 'antd/es/layout/layout'
import { useLocation, useNavigate } from 'react-router'

import ConfigLayoutHead from 'src/components/Head/ConfigLayoutHead'
import Loading from 'src/components/Loading'
import CreateNewFinalModal from 'src/components/Modal/CreateNewFinalModal'
import LeaveConfirmModal from 'src/components/Modal/LeaveConfirmModal'
import { StudyInfoTable, EmptyStructureTable } from 'src/components/Table'
import { ConfigDescriptionItem } from 'src/context/moduleList'
import useAlert from 'src/hook/useAlert'
import { useAntModal } from 'src/hook/useAntModal'
import useInvalidField from 'src/hook/useInvalidField'
import i18n from 'src/i18n'
import { EditStructureLayout, RemoteTransferTabs } from 'src/Layout'
import ConfigLayout from 'src/Layout/ConfigLayout'
import {
  useGetStructureConfigMutation,
  useGetProtocolDetailMutation,
  useGetRemoteConfigMutation,
  usePostProtocolMutation,
} from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { resetConfigState } from 'src/store/reducers/configSlice'
import {
  addStudyInfo,
  updateStudyInfo,
  deleteStudyInfo,
  addCustomizedStructures,
  updateCustomizedStructures,
  deleteCustomizedStructures,
  updateDetail,
  updateStructure,
  updateRemote,
} from 'src/store/reducers/detailSlice'
import { removeId } from 'src/utils/helper'

const btnStyle = {
  width: '6.25rem',
  lineHeight: 1.15,
  height: '2rem',
}

function CreateProtocolPage() {
  // redux
  const dispatch = useAppDispatch()
  const { configRequired } = useAppSelector((state) => state.configReducer)
  const {
    detail: detailState,
    isDetailUpdate,
  } = useAppSelector((state) => state.detailReducer) as { detail: ProtocolDetailType, isDetailUpdate: boolean }
  // state
  const [tabKey, setTabKey] = useState<string>('1')
  const [buttonKey, setButtonKey] = useState<string[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [routerBlocker, setRouterBlocker] = useState<boolean>(true)
  const [isNavigate, setIsNavigate] = useState<boolean>(false)
  // api
  const [getStructureConfigMutation] = useGetStructureConfigMutation()
  const [getProtocolDetailMutation] = useGetProtocolDetailMutation()
  const [getRemoteConfigMutation] = useGetRemoteConfigMutation()
  const [postProtocolMutation] = usePostProtocolMutation()
  // router
  const { state } = useLocation()
  const navigate = useNavigate()
  const handleAlert = useAlert()

  // fetch
  const getApiData = async () => {
    setLoading(true)
    try {
      await getStructureConfigMutation().unwrap()
      await getRemoteConfigMutation().unwrap()
      if (state) {
        const { protocolName, copyProtocolID } = state
        if (copyProtocolID !== 'create-protocol') {
          await getProtocolDetailMutation({ id: Number(copyProtocolID) }).unwrap()
        }
        dispatch(updateDetail({ protocol_name: protocolName }))
      }
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
    setLoading(false)
  }

  const protocolSaved = async () => {
    setRouterBlocker(false)
    if (isDetailUpdate) {
      try {
        await postProtocolMutation({
          protocol_name: detailState.protocol_name,
          description: detailState.description,
          structure_set_label: detailState.structure_set_label,
          status: detailState.status,
          source: detailState.source,
          destination: detailState.destination,
          structures: detailState.structures,
          study_info: removeId(detailState.study_info),
          customized_structures: removeId(detailState.customized_structures),
        }).unwrap()
        dispatch(resetConfigState())
        setIsNavigate(true)
      } catch (e) {
        setRouterBlocker(true)
        handleAlert({ title: i18n.t('error_titles.save'), content: (e as Err).data?.detail }, 'Msg', 'error')
      }
    }
  }

  // hook
  const { verifyDetail } = useInvalidField(detailState)

  const corfirmSaveModal = useAntModal({
    triggerProps: {
      async onClick() {
        try {
          await verifyDetail()
        } catch (error) {
          throw new Error('verifyDetail')
        }
      },
    },
    modalProps: {
      onOk: protocolSaved,
    },
  })

  // handlers
  const NextTab = () => setTabKey((Number(tabKey) + 1).toString())

  const PrevTab = () => {
    setTabKey((Number(tabKey) - 1).toString())
  }

  const handleUpdateStructures = (structure: number[] | number) => {
    if (typeof structure === 'number') {
      dispatch(updateStructure({ id: structure }))
    } else {
      dispatch(updateDetail({ structures: structure.map((id) => ({ id })) }))
    }
  }
  const handleAddStudyInfo = (data: StudyInformationType) => dispatch(addStudyInfo({ data }))
  const handleDeleteStudyInfo = (data: StudyInformationType) => dispatch(deleteStudyInfo({ data }))
  const handleChangeStudyInfo = (id: number, fieldName: string, data: StudyInformationType) => {
    dispatch(updateStudyInfo({ data: { id, [fieldName]: data } }))
  }
  const handleAddCustomizedStructures = () => {
    dispatch(addCustomizedStructures())
  }
  const handleDeleteCustomizedStructures = (data: CustomizedStructuresType) => {
    dispatch(deleteCustomizedStructures({ data }))
  }
  const handleChangeCustomizedStructures = (
    id: number,
    fieldName: string,
    data: CustomizedStructuresType | CustomizedStructuresChangeDataType,
  ) => {
    dispatch(updateCustomizedStructures({ data: { id, [fieldName]: data } }))
  }

  const handleUpdateRemote = (
    type: SourceDestinationKey,
    remote: RemoteCategoryKey,
    value: RemoteType[],
  ) => {
    dispatch(updateRemote({ type, remote, value }))
  }

  const handleChangeInput = (fieldName: string, value: string) => {
    dispatch(updateDetail({ [fieldName]: value }))
  }

  // data
  const configHeader: ConfigDescriptionItem[] = [
    {
      label: 'protocol_name',
      required: true,
      type: 'input',
      props: {
        value: detailState.protocol_name,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('protocol_name', e.target.value)
        },
        status: configRequired.protocol_name && 'error',
      },
    },
    {
      label: 'description',
      type: 'input',
      props: {
        value: detailState.description,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('description', e.target.value)
        },
      },
    },
    {
      label: 'structure_set_label',
      tooltip: i18n.t('tooltips.structure_set_label'),
      type: 'input',
      required: true,
      props: {
        value: detailState.structure_set_label,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('structure_set_label', e.target.value)
        },
        status: configRequired.structure_set_label && 'error',
      },
    },
    {
      label: 'status',
      type: 'switch',
      value: detailState.status,
      props: {
        checked: detailState.status === 'ACTIVE',
        onChange(value: boolean) {
          handleChangeInput('status', value ? 'ACTIVE' : 'INACTIVE')
        },
      },
    },
  ]

  const tabItems: TabsProps['items'] = [
    {
      key: '1',
      label: i18n.t('titles.source_destination'),
      children: <RemoteTransferTabs detail={detailState} onUpdateRemote={handleUpdateRemote} />,
    },
    {
      key: '2',
      label: i18n.t('titles.structures'),
      children: (
        <EditStructureLayout
          structureData={detailState.structures.map((item) => item.id)}
          handleUpdateStructures={handleUpdateStructures}
        />
      ),
      disabled: !buttonKey.includes('1'),
    },
    {
      key: '3',
      label: (
        <span className={configRequired.study_info ? 'red-ball' : ''}>
          {i18n.t('titles.study_info')}
        </span>
      ),
      children: (
        <StudyInfoTable
          data={detailState.study_info}
          onAdd={handleAddStudyInfo}
          onDelete={handleDeleteStudyInfo}
          onChange={handleChangeStudyInfo}
        />
      ),
      disabled: !['1', '2'].every((item) => buttonKey.includes(item)),
    },
    {
      key: '4',
      label: (
        <span className={configRequired.customized_structures.length ? 'red-ball' : ''}>
          {i18n.t('titles.add_customized_structures')}
        </span>
      ),
      children: (
        <EmptyStructureTable
          dataSource={detailState.customized_structures}
          onAdd={handleAddCustomizedStructures}
          onDelete={handleDeleteCustomizedStructures}
          onChange={handleChangeCustomizedStructures}
        />
      ),
      disabled: !['1', '2', '3'].every((item) => buttonKey.includes(item)),
    },
  ]

  const footerNextButton = (nowKey: string): JSX.Element => {
    let content: JSX.Element = <div />
    switch (nowKey) {
      case '1':
        content = (
          <Button
            variant="outlined"
            color="primary"
            style={btnStyle}
            onClick={NextTab}
            disabled={!buttonKey.includes('1')}
          >
            {i18n.t('buttons.next')}
          </Button>
        )
        break
      case '2':
        content = (
          <Button
            className="outline"
            style={btnStyle}
            onClick={NextTab}
            disabled={!['1', '2'].every((item) => buttonKey.includes(item))}
          >
            {i18n.t('buttons.next')}
          </Button>
        )
        break
      case '3':
        content = (
          <Button
            className="outline"
            style={btnStyle}
            onClick={NextTab}
            disabled={!['1', '2', '3'].every((item) => buttonKey.includes(item))}
          >
            {i18n.t('buttons.next')}
          </Button>
        )
        break
      case '4':
        content = (
          <Button
            className="outline"
            style={btnStyle}
            disabled={buttonKey.length !== 3}
            {...corfirmSaveModal.triggerProps}
          >
            {i18n.t('buttons.save')}
          </Button>
        )
        break

      default:
        return content
    }
    return content
  }

  useEffect(() => {
    getApiData()
    return () => {
      dispatch(resetConfigState())
    }
  }, [])

  useEffect(() => {
    const sourceCheck: boolean = (
      !detailState.source.folder.length
      && !detailState.source.remote_server.length
    )
    const structuresCheck: boolean = !detailState.structures.length
    const studyInfoCheck: boolean = !detailState.study_info.length

    if (sourceCheck) return setButtonKey([])
    if (structuresCheck) return setButtonKey(['1'])
    if (studyInfoCheck) return setButtonKey(['1', '2'])
    return setButtonKey(['1', '2', '3'])
  }, [detailState])

  useEffect(() => {
    if (isNavigate) navigate('/protocols', { replace: true })
  }, [isNavigate])

  return (
    <>
      <ConfigLayoutHead navigatePage="/protocols">{i18n.t('modal_titles.new_protocol_name')}</ConfigLayoutHead>
      <Content>
        <ConfigLayout
          configData={detailState}
          tabItems={tabItems}
          setTabKey={setTabKey}
          tabKey={tabKey}
          informationType="protocol"
        />
        <footer className="layout-footer">
          <Space size={16} style={{ marginLeft: 'auto' }}>
            {Number(tabKey) > 1
              && (
                <Button className="outline" style={btnStyle} onClick={PrevTab}>
                  {i18n.t('buttons.prev')}
                </Button>
              )}

            {footerNextButton(tabKey)}
          </Space>
        </footer>
      </Content>
      <Loading open={loading} />
      <LeaveConfirmModal condition={routerBlocker} />
      <CreateNewFinalModal
        detail={detailState}
        setRouterBlocker={setRouterBlocker}
        descriptionData={configHeader}
        {...corfirmSaveModal.modalProps}
      />
    </>
  )
}

export default CreateProtocolPage
