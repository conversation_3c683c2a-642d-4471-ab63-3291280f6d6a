import { useEffect, useState } from 'react'

import { TabsProps } from 'antd'
import { Content } from 'antd/es/layout/layout'
import { useParams, useNavigate } from 'react-router'

import ConfigLayoutHead from 'src/components/Head/ConfigLayoutHead'
import Loading from 'src/components/Loading'
import { FooterBtnGroup } from 'src/components/Modal'
import LeaveConfirmModal from 'src/components/Modal/LeaveConfirmModal'
import { StudyInfoTable, EmptyStructureTable } from 'src/components/Table'
import useAlert from 'src/hook/useAlert'
import i18n from 'src/i18n'
import { EditStructureLayout, RemoteTransferTabs } from 'src/Layout'
import ConfigLayout from 'src/Layout/ConfigLayout'
import {
  useGetProtocolDetailMutation,
  useGetStructureConfigMutation,
  useGetRemoteConfigMutation,
  usePutProtocolMutation,
} from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { resetConfigState, updateConfigRequired } from 'src/store/reducers/configSlice'
import {
  addStudyInfo,
  updateStudyInfo,
  deleteStudyInfo,
  addCustomizedStructures,
  updateCustomizedStructures,
  deleteCustomizedStructures,
  updateDetail,
  updateStructure,
  updateRemote,
} from 'src/store/reducers/detailSlice'
import { removeId, checkDicomTagConform, generateErrorMessage } from 'src/utils/helper'
import { resetButton, saveButton } from 'src/utils/saveButton'
import 'src/styles/pages/configLayout.css'

function ProtocolConfigPage() {
  const protocolId = Number(useParams().id)
  const [conformanceCheck, setConformanceCheck] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(false)
  const [routerBlocker, setRouterBlocker] = useState<boolean>(false)
  const [isNavigate, setIsNavigate] = useState<boolean>(false)
  const [modalContent, setModalContent] = useState<string>()
  const { configRequired } = useAppSelector((state) => state.configReducer)
  const {
    detail: detailState,
    isDetailUpdate,
  } = useAppSelector((state) => state.detailReducer) as { detail: ProtocolDetailType, isDetailUpdate: boolean }
  const [getProtocolDetailMutation] = useGetProtocolDetailMutation()
  const [getStructureConfigMutation] = useGetStructureConfigMutation()
  const [getRemoteConfigMutation] = useGetRemoteConfigMutation()
  const [putProtocolMutation] = usePutProtocolMutation()
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  // hook
  const handleAlert = useAlert()

  const getApiData = async () => {
    setLoading(true)
    try {
      await getStructureConfigMutation().unwrap()
      await getRemoteConfigMutation().unwrap()
      await getProtocolDetailMutation({ id: protocolId }).unwrap()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
    setLoading(false)
  }

  const updateProtocol = async () => {
    setRouterBlocker(false)
    try {
      await putProtocolMutation({
        id: protocolId,
        protocol_name: detailState.protocol_name,
        description: detailState.description,
        structure_set_label: detailState.structure_set_label,
        status: detailState.status,
        source: detailState.source,
        destination: detailState.destination,
        structures: detailState.structures,
        study_info: removeId(detailState.study_info),
        customized_structures: removeId(detailState.customized_structures),
      }).unwrap()
      setIsNavigate(true)
    } catch (e) {
      setRouterBlocker(true)
      handleAlert({ title: i18n.t('error_titles.save'), content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const handleOkSave = async () => {
    if (isDetailUpdate) {
      updateProtocol()
    } else {
      navigate('/protocols', { replace: true })
    }
  }

  const handleUpdateStructures = (structure: number[] | number) => {
    if (typeof structure === 'number') {
      dispatch(updateStructure({ id: structure }))
    } else {
      dispatch(updateDetail({ structures: structure.map((id) => ({ id })) }))
    }
  }
  const handleAddStudyInfo = (data: StudyInformationType) => dispatch(addStudyInfo({ data }))
  const handleDeleteStudyInfo = (data: StudyInformationType) => dispatch(deleteStudyInfo({ data }))
  const handleChangeStudyInfo = (id: number, fieldName: string, data: StudyInformationType) => {
    dispatch(updateStudyInfo({ data: { id, [fieldName]: data } }))
  }
  const handleAddCustomizedStructures = () => {
    dispatch(addCustomizedStructures())
  }
  const handleDeleteCustomizedStructures = (data: CustomizedStructuresType) => {
    dispatch(deleteCustomizedStructures({ data }))
  }
  const handleChangeCustomizedStructures = (
    id: number,
    fieldName: string,
    data: CustomizedStructuresType | CustomizedStructuresChangeDataType,
  ) => {
    dispatch(updateCustomizedStructures({ data: { id, [fieldName]: data } }))
  }

  const handleUpdateRemote = (
    type: SourceDestinationKey,
    remote: RemoteCategoryKey,
    value: RemoteType[],
  ) => {
    dispatch(updateRemote({ type, remote, value }))
  }

  const tabItems: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <span className={configRequired.source ? 'red-ball' : ''}>
          {i18n.t('titles.source_destination')}
        </span>
      ),
      children: <RemoteTransferTabs detail={detailState} onUpdateRemote={handleUpdateRemote} />,
    },
    {
      key: '2',
      label: (
        <span className={configRequired.structures ? 'red-ball' : ''}>
          {i18n.t('titles.structures')}
        </span>
      ),
      children: (
        <EditStructureLayout
          structureData={detailState.structures.map((item) => item.id)}
          handleUpdateStructures={handleUpdateStructures}
        />
      ),
    },
    {
      key: '3',
      label: (
        <span className={configRequired.study_info ? 'red-ball' : ''}>
          {i18n.t('titles.study_info')}
        </span>
      ),
      children: (
        <StudyInfoTable
          data={detailState.study_info}
          onAdd={handleAddStudyInfo}
          onDelete={handleDeleteStudyInfo}
          onChange={handleChangeStudyInfo}
        />
      ),
    },
    {
      key: '4',
      label: (
        <span className={configRequired.customized_structures.length ? 'red-ball' : ''}>
          {i18n.t('titles.add_customized_structures')}
        </span>
      ),
      children: (
        <EmptyStructureTable
          dataSource={detailState.customized_structures}
          onAdd={handleAddCustomizedStructures}
          onDelete={handleDeleteCustomizedStructures}
          onChange={handleChangeCustomizedStructures}
        />
      ),
    },
  ]

  const dataChecked = () => {
    let warning: string = ''
    const rules: { [key: string]: boolean } = {
      protocol_name: !detailState.protocol_name,
      structure_set_label: !detailState.structure_set_label,
      source: !detailState.source.folder.length && !detailState.source.remote_server.length,
      destination: !detailState.destination.folder.length && !detailState.destination.remote_server.length,
      structures: !detailState.structures.length,
      study_info: !detailState.study_info.length,
      customized_structures: false,
    }
    const rulesList = Object.entries(rules)
    if (rulesList.some((rule) => rule[1])) warning = generateErrorMessage(rulesList)

    const dicomTagUnfilled = !detailState.study_info.every((item) => item.dicom_tag)
    const dicomTagNotConform = detailState.study_info.some((item) => checkDicomTagConform(item.dicom_tag))
    const dicomTagDuplicated = detailState.study_info
      .some((col) => detailState.study_info
        .some((item) => col.id !== item.id && col.dicom_tag === item.dicom_tag))

    if (dicomTagUnfilled || dicomTagNotConform || dicomTagDuplicated) {
      rules.study_info = true
      if (!warning) {
        warning = i18n.t('modal_contents.wrong_with_study_info')
      }
    }

    const structureIdUnfilled = !detailState.customized_structures.every((item) => item.name)
    const structureIdDuplicated = detailState.customized_structures
      .some((col) => detailState.customized_structures
        .some((item) => col.id !== item.id && col.name === item.name))
    if (structureIdUnfilled || structureIdDuplicated) {
      rules.customized_structures = true
      if (!warning) {
        warning = i18n.t(
          'modal_contents.wrong_with_customized_structures',
          { item: `"${i18n.t('titles.add_customized_structures')}"`, joinArrays: ' ' },
        )
      }
    }

    dispatch(updateConfigRequired({ rules }))
    setConformanceCheck(!warning)
    setModalContent(warning)
  }

  const footerButtonGroup: ModalBtnType[] = [
    {
      ...resetButton,
      onOk: getApiData,
    },
    {
      ...saveButton,
      btnClick: dataChecked,
      btnName: i18n.t('buttons.save'),
      modalClassName: 'confirm-modal',
      okText: i18n.t('buttons.save'),
      onOk: conformanceCheck ? handleOkSave : undefined,
      modalTitle: conformanceCheck ? i18n.t('modal_titles.save_settings') : '',
      modalContent: conformanceCheck ? i18n.t('modal_contents.save_settings') : modalContent,
      modalFooter: conformanceCheck ? undefined : null,
      modalBodyStyle: conformanceCheck ? undefined : { padding: '7.75rem 1.25rem 8.25rem' },
    },
  ]

  useEffect(() => {
    getApiData()
    return () => {
      dispatch(resetConfigState())
    }
  }, [])

  useEffect(() => {
    setRouterBlocker(isDetailUpdate)
  }, [isDetailUpdate])

  useEffect(() => {
    if (isNavigate) navigate('/protocols', { replace: true })
  }, [isNavigate])

  return (
    <>
      <ConfigLayoutHead navigatePage="/protocols">
        {detailState.protocol_name}
      </ConfigLayoutHead>
      <Content style={{ height: 'calc(100vh - 64px)' }}>
        <ConfigLayout
          configData={detailState}
          tabItems={tabItems}
          informationType="protocol"
        />
        <FooterBtnGroup items={footerButtonGroup} style={{ marginTop: 'auto' }} />
      </Content>
      <Loading open={loading} />
      <LeaveConfirmModal condition={routerBlocker && isDetailUpdate} />
    </>
  )
}

export default ProtocolConfigPage
