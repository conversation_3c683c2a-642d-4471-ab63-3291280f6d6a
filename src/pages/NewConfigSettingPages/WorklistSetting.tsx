import React, { useState } from 'react'

import {
  Button, Checkbox, Modal, type TabsProps,
} from 'antd'
import { useNavigate } from 'react-router'

import SendToDesination from 'src/components/Form/SendToDesination'
import ConfigTabItemLabel from 'src/components/Tabs/ConfigTabItemLabel'
import TransferTabs from 'src/components/Transfer/TransferTabs'
import type { ConfigDescriptionItem } from 'src/context/moduleList'
import { useAntModal } from 'src/hook/useAntModal'
import useInvalidField from 'src/hook/useInvalidField'
import i18n from 'src/i18n'
import { BasicLayout, StructuresTabs } from 'src/Layout/NewConfigLayout'
import { useAppSelector } from 'src/store/hook'

import useWorklistDetailState from './useWorklistDetailState'

interface Props {
  isHistory?: boolean
}

function WorklistSetting({ isHistory }: Props) {
  const navigate = useNavigate()
  // state
  const [isCorfirmModal, setCorfirmModal] = useState<boolean>(true)
  const [selectValue, setSelectValue] = useState<string>('')

  // redux
  const { protocolConfig, configRequired } = useAppSelector((state) => state.configReducer)

  // hook
  const {
    detailState, loading, leaveBlocker,
    handleChangeUseProtocol,
    handleChangeInput,
    handleUpdateWorklistRemote,
    handleOk,
  } = useWorklistDetailState(isHistory)
  const { verifyDetail } = useInvalidField(detailState)

  // function
  const handleRemoveDestination = () => {
    handleUpdateWorklistRemote('destination', 'remote_server', [])
    handleUpdateWorklistRemote('destination', 'folder', [])
  }

  const corfirmModal = useAntModal({
    modalProps: {
      title: i18n.t('modal_titles.change_protocol_confirmation'),
      okText: i18n.t('buttons.ok'),
      onOk() {
        handleChangeUseProtocol(selectValue)
      },
      centered: true,
      width: 350,
      styles: {
        body: { padding: '2.5rem 2rem' },
      },
    },
  })

  const saveModal = useAntModal({
    triggerProps: {
      children: isHistory ? i18n.t('buttons.draw') : i18n.t('buttons.save'),
      color: 'primary',
      variant: 'outlined',
      style: { width: 100 },
      onClick: verifyDetail,
    },
    modalProps: {
      title: isHistory ? i18n.t('modal_titles.redraw_series') : i18n.t('modal_titles.save_settings'),
      okText: isHistory ? i18n.t('buttons.draw') : i18n.t('buttons.save'),
      onOk: handleOk,
      centered: true,
      width: 350,
      children: isHistory ? i18n.t('modal_contents.redraw_series') : i18n.t('modal_contents.save_settings'),
      styles: {
        body: { fontSize: '18px', padding: '5rem 2rem' },
      },
    },
  })

  // data
  const configHeader: ConfigDescriptionItem[] = [
    { label: 'patient_id', value: detailState.patient_id },
    {
      label: 'structure_set_label',
      tooltip: i18n.t('tooltips.structure_set_label'),
      type: 'input',
      required: true,
      props: {
        value: detailState.structure_set_label,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('structure_set_label', e.target.value)
        },
        status: configRequired.structure_set_label && 'error',
      },
    },
    {
      label: 'use_protocol',
      type: 'select',
      props: {
        options: protocolConfig.map((item) => ({ value: item.id, label: item.protocol_name })),
        value: detailState.use_protocol ?? i18n.t('titles.customized'),
        onChange(val: string) {
          if (isCorfirmModal) {
            corfirmModal.trigger()
            setSelectValue(val)
          } else {
            handleChangeUseProtocol(val)
          }
        },
      },
    },
    { label: 'study_date', value: detailState.study_date },
    { label: 'study_description', value: detailState.study_description },
    {
      label: 'protocol_description',
      type: 'input',
      props: {
        value: detailState.protocol_description,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('protocol_description', e.target.value)
        },
      },
    },
    { label: 'series_time', value: detailState.series_time },
    { label: 'series_description', value: detailState.series_description },
  ]

  const tabItems: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <ConfigTabItemLabel warn={configRequired.destination}>
          {i18n.t('titles.destination')}
        </ConfigTabItemLabel>
      ),
      children: (
        <SendToDesination
          defaultOpen={Object.values(detailState.destination).some((list) => list.length > 0)}
          onChange={(checked) => !checked && handleRemoveDestination()}
        >
          <TransferTabs
            type="destination"
            checkList={detailState}
            onUpdateRemote={handleUpdateWorklistRemote}
          />
        </SendToDesination>
      ),
    },
    {
      key: '2',
      label: (
        <ConfigTabItemLabel warn={(configRequired.structures || !!configRequired.customized_structures.length)}>
          {i18n.t('titles.structures')}
        </ConfigTabItemLabel>
      ),
      children: (
        <StructuresTabs />
      ),
    },
  ]

  return (
    <>
      <BasicLayout
        header={{
          navigatePage: isHistory ? '/history' : '/',
          children: i18n.t('titles.config_setting'),
        }}
        descriptionData={configHeader}
        loading={loading}
        tabs={tabItems}
        showAlert={leaveBlocker && !detailState.use_protocol}
        footer={(
          <>
            {isHistory || (
              <Button
                htmlType="button"
                variant="outlined"
                color="primary"
                onClick={() => navigate('/', { replace: true })}
                style={{ width: 100 }}
              >
                {i18n.t('buttons.cancel')}
              </Button>
            )}
            <Button {...saveModal.triggerProps} />
          </>
        )}
        leaveBlocker={leaveBlocker}
      />
      {/* save & draw moda; */}
      <Modal {...saveModal.modalProps} />

      {/* confirm change use protocol modal */}
      <Modal {...corfirmModal.modalProps}>
        <p style={{
          marginBottom: '1.5rem',
          textAlign: 'center',
        }}
        >{i18n.t('modal_contents.change_protocol_confirmation')}
        </p>
        <Checkbox
          onChange={(e) => setCorfirmModal(!e.target.checked)}
        >
          {i18n.t('checkboxes.not_ask_again')}
        </Checkbox>
      </Modal>
    </>
  )
}

export default WorklistSetting
