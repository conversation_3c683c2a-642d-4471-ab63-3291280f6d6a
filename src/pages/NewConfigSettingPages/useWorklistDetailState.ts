import { useEffect, useState } from 'react'

import { useNavigate, useParams } from 'react-router'

import useAlert from 'src/hook/useAlert'
import useSortStructure from 'src/hook/useSortStructure'
import i18n from 'src/i18n'
import {
  useGetProtocolConfigMutation,
  useGetRemoteConfigMutation,
  useGetStructureConfigMutation,
  useGetWorklistDetailMutation,
  useGetWorklistProtocolDetailMutation,
  usePostWorklistRedrawMutation,
  usePutWorklistMutation,
} from 'src/services/api'
import { useAppDispatch, useAppSelector } from 'src/store/hook'
import { updateConfigRequired } from 'src/store/reducers/configSlice'
import { updateInput, updateRemote, updateProtocol } from 'src/store/reducers/detailSlice'
import { removeId } from 'src/utils/helper'

const useWorklistDetailState = (isHistory?: boolean) => {
  // state
  const [loading, setLoading] = useState(true)
  const [routerBlocker, setRouterBlocker] = useState<boolean>(false)
  const [routerConfig, setRouterConfig] = useState<ToNav>({
    state: false,
    url: isHistory ? '/history' : '/',
  })

  // redux
  const dispatch = useAppDispatch()
  const {
    detail: detailState,
    isDetailUpdate,
  } = useAppSelector((state) => state.detailReducer) as { detail: WorklistDetailType, isDetailUpdate: boolean }
  const { configRequired } = useAppSelector((state) => state.configReducer)
  // router
  const worklistId = Number(useParams().id)
  const navigate = useNavigate()
  // hook
  const { updateSortAfterSendApi } = useSortStructure()
  const handleAlert = useAlert()

  // api
  const [getWorklistDetailMutation] = useGetWorklistDetailMutation()
  const [getStructureConfigMutation] = useGetStructureConfigMutation()
  const [getRemoteConfigMutation] = useGetRemoteConfigMutation()
  const [getProtocolConfigMutation] = useGetProtocolConfigMutation()
  const [getWorklistProtocolDetailMutation] = useGetWorklistProtocolDetailMutation()
  const [postWorklistRedrawMutation] = usePostWorklistRedrawMutation()
  const [putWorklistMutation] = usePutWorklistMutation()

  const getApiData = async () => {
    try {
      await getStructureConfigMutation().unwrap()
      await getRemoteConfigMutation().unwrap()
      await getProtocolConfigMutation().unwrap()
      await getWorklistDetailMutation({ id: worklistId }).unwrap()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    } finally {
      setLoading(false)
    }
  }

  // handlers
  const handleChangeInput = (label: string, value: string) => {
    dispatch(updateInput({ label, value }))
    if (label in configRequired && value.length > 0) {
      dispatch(updateConfigRequired({ rules: { [label]: false } }))
    }
  }

  const handleChangeUseProtocol = async (value?: string) => {
    const protocolId = Number(value)
    if (!protocolId) return
    try {
      await getWorklistProtocolDetailMutation({ id: protocolId }).unwrap()
      dispatch(updateProtocol({ value: protocolId }))
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const handleUpdateRemote = (type: SourceDestinationKey, remote: RemoteCategoryKey, value: RemoteType[]) => {
    dispatch(updateRemote({ type, remote, value }))
  }

  //  handle ok in save modal or draw modal
  const updateWorklist = async () => {
    setRouterBlocker(false)
    const { updatedStructures, updatedCustomizedStructures } = await updateSortAfterSendApi()
    try {
      await putWorklistMutation({
        id: worklistId,
        data: detailState.use_protocol ? {
          protocol_id: detailState.use_protocol,
        } : {
          structure_set_label: detailState.structure_set_label,
          description: detailState.protocol_description,
          destination: detailState.destination,
          structures: updatedStructures,
          customized_structures: removeId(updatedCustomizedStructures),
        },
      }).unwrap()
      setRouterConfig((prev) => ({ ...prev, state: true }))
    } catch (e) {
      setRouterBlocker(true)
      console.error('(e as Err).data?.detail,', (e as Err).data?.detail)

      handleAlert({
        title: i18n.t('error_titles.save'),
        content: (e as Err).data?.detail,
      }, 'Msg', 'error')
    }
  }

  const redraw = async () => {
    try {
      const { updatedStructures, updatedCustomizedStructures } = await updateSortAfterSendApi()
      await postWorklistRedrawMutation({
        worklist_id: worklistId,
        data: detailState.use_protocol ? {
          protocol_id: detailState.use_protocol,
        } : {
          structure_set_label: detailState.structure_set_label,
          description: detailState.protocol_description,
          destination: detailState.destination,
          structures: updatedStructures,
          customized_structures: removeId(updatedCustomizedStructures),
        },
      }).unwrap()
      setRouterConfig({ state: true, url: '/' })
      setRouterBlocker(false)
    } catch (e) {
      setRouterBlocker(true)
      const content = JSON.stringify((e as Err).data?.detail)

      handleAlert({ title: i18n.t('error_titles.draw'), content }, 'Msg', 'error')
    }
  }
  const handleSave = () => {
    if (isDetailUpdate) {
      updateWorklist()
    } else {
      navigate(routerConfig.url, { replace: true })
    }
  }

  useEffect(() => {
    getApiData()
  }, [])

  useEffect(() => {
    setRouterBlocker(isDetailUpdate)
  }, [isDetailUpdate])

  useEffect(() => {
    if (routerConfig.state) navigate(routerConfig.url, { replace: true })
  }, [routerConfig])

  return {
    detailState,
    loading,
    leaveBlocker: routerBlocker && isDetailUpdate,
    handleChangeUseProtocol,
    handleChangeInput,
    handleUpdateWorklistRemote: handleUpdateRemote,
    handleOk: isHistory ? redraw : handleSave,
  }
}

export default useWorklistDetailState
