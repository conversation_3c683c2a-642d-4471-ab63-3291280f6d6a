import { DownIcon } from 'src/assets/icons'

interface ExpandIconProps<T = Record<string, unknown>> {
  expanded: boolean
  onExpand: (record: T, event: React.MouseEvent<HTMLElement>) => void
  record: T
}

/**
 * 通用的展開圖示元件，用於 HistoryWorklistTable 的各層級表格
 * 支援泛型，可以適用於不同的記錄類型
 */
function ExpandIcon<T = Record<string, unknown>>({ expanded, onExpand, record }: ExpandIconProps<T>) {
  return (
    <DownIcon
      style={{
        position: 'absolute',
        zIndex: 10,
        top: '50%',
        left: '16px',
        transform: expanded ? 'rotate(0deg)' : 'rotate(-90deg)',
        translate: '0 -50%',
        transition: 'transform 0.2s',
        cursor: 'pointer',
        color: 'var(--color-gray_0)',
      }}
      onClick={(event) => onExpand(record, event as unknown as React.MouseEvent<HTMLElement>)}
    />
  )
}

export default ExpandIcon
export type { ExpandIconProps }
