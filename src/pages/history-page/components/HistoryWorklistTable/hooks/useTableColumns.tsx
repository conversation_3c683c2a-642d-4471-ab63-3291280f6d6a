import React from 'react'

import { Divider, Flex, Table } from 'antd'
import type { TableColumnsType } from 'antd'

import OperationBtn from 'src/components/Button/OperationBtn'
import i18n from 'src/i18n'
import { WorkStatusEnum } from 'src/utils/enum'
import { withoutSecond, formattedStatus } from 'src/utils/helper'

// 定義樹形數據類型
interface TreeDataType {
  key: React.ReactNode
  list_id?: number
  group_id?: number
  study_date?: string
  study_description?: string
  modality?: ModalityType | ModalityType[]
  images?: number
  series_number?: number
  series_description?: string
  source?: string
  status?: string
  re_set_label?: string
  level: 'main' | 'group' | 'item'
  children?: TreeDataType[]
}

/**
 * Custom hook for managing history tree table columns
 */
export function useHistoryTreeTableColumns(
  worklistGroupFocusInfo?: WorklistGroupType,
) {
  // 樹形表格 columns
  const treeColumns: TableColumnsType<TreeDataType> = [
    {
      title: i18n.t('titles.operation'),
      key: 'operation',
      onCell: (record) => ({
        colSpan: record.level === 'main' ? 8 : 1,
      }),
      render: (record) => {
        // Main 層級
        if (record.level === 'main') {
          return (
            <Flex
              gap={8}
              align="center"
              style={{
                marginLeft: 40,
                color: 'var(--color-gray_1)',
                fontWeight: 700,
                fontSize: 16,
              }}
            >
              <OperationBtn
                worklistId={record.list_id}
                seriesInfo={{
                  patientID: worklistGroupFocusInfo?.patient_id || '',
                  studyDate: record.study_date || '',
                  series: 0,
                  seriesDescription: record.study_description || '',
                }}
                status={WorkStatusEnum.SUCCEEDED}
              />
              <span>{withoutSecond(record.study_date || '')}</span>
              <Divider type="vertical" style={{ margin: 0 }} />
              <span>
                {Array.isArray(record.modality) ? record.modality.join(', ') : record.modality}
              </span>
              <Divider type="vertical" style={{ margin: 0 }} />
              <span>{record.study_description}</span>
            </Flex>
          )
        }

        // Group 和 Item 層級
        return (
          <div style={{ marginLeft: 32 }}>
            <OperationBtn
              worklistId={record.list_id}
              seriesInfo={{
                patientID: worklistGroupFocusInfo?.patient_id || '',
                studyDate: record.study_date || '',
                series: record.series_number || 0,
                seriesDescription: record.series_description || '',
              }}
              status={record.status || WorkStatusEnum.SUCCEEDED}
            />
          </div>
        )
      },
    },
    {
      title: 'Modality',
      key: 'modality_header',
      onCell: (record) => ({
        colSpan: record.level === 'main' ? 0 : 1, // 被第一個欄位合併
      }),
      render: (_, record) => {
        if (record.level === 'main') return null
        return <div style={{ width: 100 }}>{record.modality || '-'}</div>
      },
    },
    {
      title: 'Images',
      key: 'images_header',
      onCell: (record) => ({
        colSpan: record.level === 'main' ? 0 : 1,
      }),
      render: (_, record) => {
        if (record.level === 'main') return null
        return record.images || '-'
      },
    },
    {
      title: 'RS Set Label',
      key: 'rs_set_label_header',
      onCell: (record) => ({
        colSpan: record.level === 'main' ? 0 : 1,
      }),
      render: (_, record) => {
        if (record.level === 'main') return null
        return <div style={{ width: 35 }}>{record.re_set_label || '-'}</div>
      },
    },
    {
      title: <div style={{ width: 100 }}>Series Number</div>,
      key: 'series_number_header',
      onCell: (record) => ({
        colSpan: record.level === 'main' ? 0 : 1,
      }),
      render: (_, record) => {
        if (record.level === 'main') return null
        return record.series_number || '-'
      },
    },
    {
      title: 'Series Description',
      key: 'series_description_header',
      onCell: (record) => ({
        colSpan: record.level === 'main' ? 0 : 1,
      }),
      render: (_, record) => {
        if (record.level === 'main') return null
        return record.series_description || '-'
      },
    },
    {
      title: 'Source',
      key: 'source_header',
      onCell: (record) => ({
        colSpan: record.level === 'main' ? 0 : 1,
      }),
      render: (_, record) => {
        if (record.level === 'main') return null
        return record.source || '-'
      },
    },
    {
      title: 'Status',
      key: 'status_header',
      onCell: (record) => ({
        colSpan: record.level === 'main' ? 0 : 1,
      }),
      render: (_, record) => {
        if (record.level === 'main') return null
        return record.status ? formattedStatus(record.status) : '-'
      },
    },
  ]

  return {
    treeColumns,
  }
}
