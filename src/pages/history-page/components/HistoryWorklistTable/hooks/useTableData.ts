/**
 * Custom hook for managing history tree table data
 * Converts three-level nested data structure to Antd tree table format
 */
export function useHistoryTreeTableData(mockData: HistoryWorklistType[]) {
  // 將三層嵌套數據轉換為樹形結構
  const treeData = mockData.map((mainItem) => {
    const mainNode = {
      key: `main-${mainItem.list_id}`,
      list_id: mainItem.list_id,
      study_date: mainItem.study_date,
      study_description: mainItem.study_description,
      modality: mainItem.modality,
      level: 'main' as const,
      children: mainItem.group?.map((groupItem) => {
        const groupNode = {
          key: `group-${mainItem.list_id}-${groupItem.group_id}`,
          group_id: groupItem.group_id,
          modality: groupItem.modality,
          images: groupItem.images,
          series_number: groupItem.series_number,
          series_description: groupItem.series_description,
          source: groupItem.source,
          status: groupItem.status,
          level: 'group' as const,
          children: groupItem.items?.map((itemData) => ({
            key: `item-${mainItem.list_id}-${groupItem.group_id}-${itemData.group_id}`,
            group_id: itemData.group_id,
            modality: itemData.modality,
            images: itemData.images,
            series_number: itemData.series_number,
            series_description: itemData.series_description,
            source: itemData.source,
            status: itemData.status,
            level: 'item' as const,
          })) || [],
        }
        return groupNode
      }) || [],
    }
    return mainNode
  })

  return {
    treeData,
  }
}
