import 'src/styles/components/tableCascader.css'

import { useWorklistOperations } from 'src/hooks/useWorklistOperations'
import IndexTableLayout from 'src/Layout/IndexTableLayout'

import HistoryWorklistTable from './components/HistoryWorklistTable'
import { HISTORY_OPTIONS } from './constants'

/**
 * History page component for completed worklist
 */
function HistoryPage() {
  const {
    form,
    groupTableLoading,
    tableLoading,
    groupSorted,
    setHistorySorted,
    getWorklistGroup,
  } = useWorklistOperations(true)

  return (
    <IndexTableLayout
      pageTitle="titles.history"
      searchOptions={HISTORY_OPTIONS}
      isHistoryPage
      form={form}
      groupTableLoading={groupTableLoading}
      groupSorted={groupSorted}
      handleSearch={getWorklistGroup}
    >
      <HistoryWorklistTable
        tableLoading={tableLoading}
        setSortedInfo={setHistorySorted}
      />
    </IndexTableLayout>
  )
}

export default HistoryPage
