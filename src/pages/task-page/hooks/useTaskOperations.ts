import { useEffect } from 'react'

import { Form } from 'antd'

import { useTableState } from 'src/hooks/useTableState'
import { useWorklistData } from 'src/hooks/useWorklistData'
import {
  resetWorklistState,
  updateCurrentPage,
  updateWorklistFocus,
  updateWorklistGroupSearch,
  clearWorklist,
} from 'src/store/reducers/worklistSlice'

import type { SorterInfo } from '../types'
import { extractFormValues, calculateSortingParams } from '../utils/taskHelpers'

/**
 * Custom hook for task operations and business logic
 */
export function useTaskOperations() {
  const [form] = Form.useForm()

  const {
    pathname,
    dispatch,
    handleAlert,
    worklistGroupFocus,
    worklistGroupPage,
    worklistGroupSearch,
    message,
    getWorklistGroupMutation,
    reGetWorklistGroupMutation,
    reGetWorklistMutation,
  } = useWorklistData()

  const {
    groupTableLoading,
    setGroupTableLoading,
    tableLoading,
    setTableLoading,
    groupSorted,
    setGroupSorted,
    worklistSorted,
    setWorklistSorted,
  } = useTableState()

  const getWorklistGroup = async (page?: number, sorter?: SorterInfo) => {
    setGroupTableLoading(true)

    try {
      const formValues = extractFormValues(form)
      const { nextPage, nextOrderKey, nextOrder } = calculateSortingParams(
        page,
        sorter,
        worklistGroupSearch,
      )

      await getWorklistGroupMutation({
        page: nextPage,
        patient_id: formValues.patientId,
        study_status: formValues.studyStatus,
        study_date_range_start: formValues.studyDateRangeStart,
        study_date_range_end: formValues.studyDateRangeEnd,
        order_key: nextOrderKey,
        ascend: nextOrder,
        history: false,
      }).unwrap()

      if (sorter) {
        setGroupSorted(sorter)
      }

      dispatch(updateCurrentPage({ page: nextPage }))
      dispatch(updateWorklistGroupSearch({
        patient_id: formValues.patientId,
        study_status: formValues.studyStatus,
        study_date_range_start: formValues.studyDateRangeStart,
        study_date_range_end: formValues.studyDateRangeEnd,
        order_key: nextOrderKey,
        ascend: nextOrder,
      }))
    } catch (error) {
      handleAlert({ content: (error as Err).data?.detail }, 'Msg', 'error')
    } finally {
      setGroupTableLoading(false)
    }
  }

  const reGetWorklistGroup = async () => {
    try {
      await reGetWorklistGroupMutation({
        page: worklistGroupPage.current,
        patient_id: worklistGroupSearch.patient_id,
        study_status: worklistGroupSearch.study_status,
        study_date_range_start: worklistGroupSearch.study_date_range_start,
        study_date_range_end: worklistGroupSearch.study_date_range_end,
        order_key: worklistGroupSearch.order_key,
        ascend: worklistGroupSearch.ascend,
        history: false,
      }).unwrap()
    } catch (error) {
      // Don't use useAlert for polling requests
      console.error('Failed to refresh worklist group:', error)
    }
  }

  const reGetWorklist = async (worklistGroupId: number) => {
    setTableLoading(true)

    try {
      await reGetWorklistMutation({ worklist_group_id: worklistGroupId }).unwrap()
    } catch (error) {
      handleAlert({ content: (error as Err).data?.detail }, 'Msg', 'error')
    } finally {
      setTableLoading(false)
    }
  }

  // Effects - 簡單的依賴，只在 pathname 改變時重新載入
  useEffect(() => {
    getWorklistGroup()

    return () => {
      form.resetFields()
      setGroupSorted({})
      setWorklistSorted({})
      dispatch(updateWorklistFocus({ id: undefined }))
      dispatch(resetWorklistState())
    }
  }, [pathname])

  useEffect(() => {
    if (worklistGroupFocus.id) {
      reGetWorklist(worklistGroupFocus.id)
    } else {
      dispatch(clearWorklist())
    }
  }, [worklistGroupFocus.timeStamp])

  useEffect(() => {
    if (message.content === 'worklist_update') {
      reGetWorklistGroup()
    }
  }, [message.timestamp])

  return {
    form,
    groupTableLoading,
    tableLoading,
    groupSorted,
    worklistSorted,
    setWorklistSorted,
    getWorklistGroup,
  }
}
