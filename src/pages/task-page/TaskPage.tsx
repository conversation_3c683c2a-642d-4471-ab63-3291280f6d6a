import 'src/styles/components/tableCascader.css'

import { useWorklistOperations } from 'src/hooks/useWorklistOperations'
import IndexTableLayout from 'src/Layout/IndexTableLayout'

import WorklistTable from './components/WorklistTable'
import { STATUS_OPTIONS } from './constants'

/**
 * Task list page component for active worklist
 */
function TaskPage() {
  const {
    form,
    groupTableLoading,
    tableLoading,
    groupSorted,
    worklistSorted,
    setWorklistSorted,
    getWorklistGroup,
  } = useWorklistOperations(false)

  return (
    <IndexTableLayout
      pageTitle="titles.task_list"
      searchOptions={STATUS_OPTIONS}
      isHistoryPage={false}
      form={form}
      groupTableLoading={groupTableLoading}
      groupSorted={groupSorted}
      handleSearch={getWorklistGroup}
    >
      <WorklistTable
        tableLoading={tableLoading}
        sortedInfo={worklistSorted}
        setSortedInfo={setWorklistSorted}
      />
    </IndexTableLayout>
  )
}

export default TaskPage
