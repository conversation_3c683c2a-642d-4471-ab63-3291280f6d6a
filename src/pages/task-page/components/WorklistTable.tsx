import {
  Config<PERSON>rovider, Flex, Table, Toolt<PERSON>, Switch,
} from 'antd'
import type { ColumnsType, SorterResult } from 'antd/es/table/interface'
import { useNavigate } from 'react-router'

import { LoadingIcon2, Setting3 } from 'src/assets/icons'
import Button from 'src/components/Button'
import SeriesDataNavbar from 'src/components/Nav/SeriesDataNavbar'
import useAlert from 'src/hook/useAlert'
import i18n from 'src/i18n'
import { useGetWorklistGroupMutation, useGetWorklistMutation, usePatchWorklistStatusMutation } from 'src/services/api'
import { useAppSelector } from 'src/store/hook'
import { store } from 'src/store/index'
import { color } from 'src/styles/utils/variables'
import { WorkStatusEnum } from 'src/utils/enum'
import {
  withoutSecond, formattedStatus,
} from 'src/utils/helper'

interface Props {
  tableLoading: boolean
  sortedInfo: SorterResult<SeriesType>
  setSortedInfo: (sorter: SorterResult<SeriesType>) => void;
}

/**
 * Active task list table component (non-history)
 */
function WorklistTable({
  tableLoading, sortedInfo, setSortedInfo,
}: Props) {
  const {
    worklistGroupPage, worklistGroupSearch, worklistGroup, worklistGroupFocus, worklist,
  } = useAppSelector((state) => state.worklistReducer)
  const selectedWorklistGroup = worklistGroup.find((word) => word.id === worklistGroupFocus.id)
  const [patchWorklistStatusMutation] = usePatchWorklistStatusMutation()
  const [getWorklistGroupMutation] = useGetWorklistGroupMutation()
  const [getWorklistMutation] = useGetWorklistMutation()
  const navigate = useNavigate()
  const handleAlert = useAlert()

  // function
  const getworklistGroupFocus = async () => {
    // get the worklistGroupFocus immediately
    const state = store.getState()
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const { worklistGroupFocus } = state.worklistReducer

    if (worklistGroupFocus.id) {
      await getWorklistMutation({ worklist_group_id: worklistGroupFocus.id }).unwrap()
    }
  }

  const handleSwitchChange = async (id: number, switch_status: string) => {
    try {
      await patchWorklistStatusMutation({ id, status: switch_status }).unwrap()
      await getWorklistGroupMutation({
        page: worklistGroupPage.current,
        patient_id: worklistGroupSearch.patient_id,
        study_status: worklistGroupSearch.study_status,
        study_date_range_start: worklistGroupSearch.study_date_range_start,
        study_date_range_end: worklistGroupSearch.study_date_range_end,
        order_key: worklistGroupSearch.order_key,
        ascend: worklistGroupSearch.ascend,
        history: false,
      }).unwrap()
      await getworklistGroupFocus()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const textDisabled = selectedWorklistGroup?.study_status === WorkStatusEnum.SUSPENDED && 'text-disabled'
  const seriesConfigBtn: string[] = [WorkStatusEnum.PENDING, WorkStatusEnum.CANCELLED]

  const columns: ColumnsType<SeriesType> = [
    {
      title: i18n.t('titles.operation'),
      dataIndex: 'operation',
      key: 'operation',
      width: '8.75rem',
      render: (_, {
        worklist_id,
        status,
      }) => (
        <Flex component="nav" gap={12}>
          <Tooltip
            title={i18n.t('tooltips.worklist_operation')}
            color="var(--color-gray_2)"
          >
            <Button
              className="icon-only"
              disabled={!seriesConfigBtn.includes(status)}
              onClick={() => (navigate(`/worklist/${worklist_id}`))}
            >
              <Setting3 width={20} height={20} />
            </Button>
          </Tooltip>
        </Flex>
      )

      ,
    },
    {
      title: i18n.t('titles.series'),
      dataIndex: 'number',
      key: 'number',
      width: '100px',
      sorter: false,
      sortOrder: sortedInfo.columnKey === 'number' ? sortedInfo.order : null,
      render: (_, { number }) => (
        <span
          className={`${textDisabled}`}
          style={{ display: 'block', minWidth: '60px', textIndent: '4px' }}
        >
          {number}
        </span>
      ),
    },
    {
      title: i18n.t('titles.series_time'),
      dataIndex: 'time',
      key: 'time',
      width: '180px',
      sorter: false,
      sortOrder: sortedInfo.columnKey === 'time' ? sortedInfo.order : null,
      showSorterTooltip: false,
      render: (_, { time }) => (
        <p
          className={`${textDisabled}`}
          style={{ textWrap: 'nowrap' }}
        >
          {withoutSecond(time)}
        </p>
      ),
    },
    {
      title: i18n.t('titles.last_modified'),
      dataIndex: 'last_modified',
      key: 'last_modified',
      width: '180px',
      sorter: false,
      sortOrder: sortedInfo.columnKey === 'last_modified' ? sortedInfo.order : null,
      showSorterTooltip: false,
      render: (_, { last_modified }) => (
        <p
          className={`${textDisabled}`}
          style={{ textWrap: 'nowrap' }}
        >
          {withoutSecond(last_modified)}
        </p>
      ),
    },
    {
      title: i18n.t('titles.image'),
      dataIndex: 'image',
      key: 'image',
      width: '120px',
      sorter: false,
      sortOrder: sortedInfo.columnKey === 'image' ? sortedInfo.order : null,
      render(_, { image }) {
        return (
          <span
            className={`${textDisabled}`}
            style={{ display: 'block', minWidth: '60px', textIndent: '4px' }}
          >
            {image}
          </span>
        )
      },
    },
    {
      title: i18n.t('titles.series_description'),
      dataIndex: 'description',
      key: 'description',
      sorter: false,
      sortOrder: sortedInfo.columnKey === 'description' ? sortedInfo.order : null,
      render(_, { description }) {
        return (
          <div
            className={`${textDisabled}`}
            style={{ whiteSpace: 'break-spaces', minWidth: '300px' }}
          >
            {description}
          </div>
        )
      },

    },
    {
      title: i18n.t('titles.status'),
      dataIndex: 'status',
      key: 'status',
      width: '100px',
      sorter: false,
      sortOrder: sortedInfo.columnKey === 'status' ? sortedInfo.order : null,
      showSorterTooltip: false,
      render: (_, { worklist_id, status }) => {
        if (seriesConfigBtn.includes(status)) {
          return (
            <Flex gap={8} align="center" justify="space-between" style={{ width: '115px' }}>
              <span
                className={`progress-title ${`${textDisabled}`}`}
              >
                {i18n.t(
                  status === WorkStatusEnum.PENDING
                    ? 'switch_options.pending' : 'switch_options.cancelled',
                )}
              </span>
              <Tooltip title={seriesConfigBtn.includes(status) ? i18n.t('tooltips.worklist_status_switch') : ''}>
                <Switch
                  checked={status === WorkStatusEnum.PENDING}
                  onChange={(bool: boolean) => {
                    handleSwitchChange(worklist_id, bool ? WorkStatusEnum.PENDING : WorkStatusEnum.CANCELLED)
                  }}
                  style={{ border: '1px solid white' }}
                  disabled={selectedWorklistGroup?.study_status === WorkStatusEnum.SUSPENDED}
                />
              </Tooltip>
            </Flex>
          )
        }

        return (
          <div
            className={`progress-title ${`${textDisabled}`}`}
          >
            {formattedStatus(status)}
            <span style={{ fontSize: '.75rem' }}>
              {/* Active worklist doesn't show protocol name */}
            </span>
          </div>
        )
      },
    },
  ]

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            colorBgContainer: color.gray[3],
            rowHoverBg: color.gray[2],
            headerBg: color.gray[3],
            headerSortHoverBg: color.gray[2],
            headerSortActiveBg: color.gray[3],
            bodySortBg: color.gray[3],
            colorPrimary: color.primary.default,
          },
        },
      }}
      table={{
        className: 'series-table-container',
      }}
    >
      <section
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          padding: '.75rem 1rem',
          background: color.gray[3],
        }}
      >
        <SeriesDataNavbar history={false} />
        <Table
          columns={columns}
          dataSource={worklist.series}
          loading={{
            spinning: tableLoading,
            delay: 500,
            indicator: <LoadingIcon2 className="spin-animation" />,
          }}
          pagination={false}
          rowKey={(record) => record.worklist_id}
          scroll={{ x: true, y: 'calc(100% - 61px)' }}
          onChange={(_pagination, _filters, sorter) => {
            setSortedInfo(sorter as SorterResult<SeriesType>)
          }}
        />
      </section>
    </ConfigProvider>
  )
}

export default WorklistTable
