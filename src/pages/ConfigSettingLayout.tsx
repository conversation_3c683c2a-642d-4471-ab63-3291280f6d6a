import { useEffect, useState } from 'react'

import { TabsProps } from 'antd'
import { Content } from 'antd/es/layout/layout'
import { To, useNavigate, useParams } from 'react-router'

import SendToDesination from 'src/components/Form/SendToDesination'
import ConfigLayoutHead from 'src/components/Head/ConfigLayoutHead'
import Loading from 'src/components/Loading'
import { FooterBtnGroup } from 'src/components/Modal'
import LeaveConfirmModal from 'src/components/Modal/LeaveConfirmModal'
import { EmptyStructureTable } from 'src/components/Table'
import TransferTabs from 'src/components/Transfer/TransferTabs'
import useAlert from 'src/hook/useAlert'
import i18n from 'src/i18n'
import { EditStructureLayout } from 'src/Layout'
import ConfigLayout from 'src/Layout/ConfigLayout'
import {
  useGetStructureConfigMutation,
  useGetRemoteConfigMutation,
  useGetWorklistDetailMutation,
  useGetProtocolConfigMutation,
  usePutWorklistMutation,
  usePostWorklistRedrawMutation,
} from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { resetConfigState, updateConfigRequired } from 'src/store/reducers/configSlice'
import {
  clearWorklistDetail,
  updateWorklistDetail,
  updateWorklistRemote,
  updateWorklistStructure,
  addWorklistCustomizedStructures,
  updateWorklistCustomizedStructures,
  deleteWorklistCustomizedStructures,
} from 'src/store/reducers/worklistSlice'
import { removeId, generateErrorMessage } from 'src/utils/helper'

interface Props {
  draw?: boolean
  navigatePage?: To
}

function ConfigSettingLayout({ draw = false, navigatePage }: Props) {
  const worklistId = Number(useParams().id)
  // state
  const [conformanceCheck, setConformanceCheck] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(false)
  const [routerBlocker, setRouterBlocker] = useState<boolean>(false)
  const [modalContent, setModalContent] = useState<string>('')
  const [routerConfig, setRouterConfig] = useState<ToNav>({
    state: false,
    url: draw ? '/history' : '/',
  })
  const { configRequired } = useAppSelector((state) => state.configReducer)
  const { worklistDetail, updateWorklistDetailCheck } = useAppSelector((state) => state.worklistReducer)

  // api
  const [getWorklistDetailMutation] = useGetWorklistDetailMutation()
  const [getStructureConfigMutation] = useGetStructureConfigMutation()
  const [getRemoteConfigMutation] = useGetRemoteConfigMutation()
  const [getProtocolConfigMutation] = useGetProtocolConfigMutation()
  const [putWorklistMutation] = usePutWorklistMutation()
  const [postWorklistRedrawMutation] = usePostWorklistRedrawMutation()
  const dispatch = useAppDispatch()
  const navigate = useNavigate()

  // hook
  const handleAlert = useAlert()

  const getApiData = async () => {
    setLoading(true)
    try {
      await getStructureConfigMutation().unwrap()
      await getRemoteConfigMutation().unwrap()
      await getProtocolConfigMutation().unwrap()
      await getWorklistDetailMutation({ id: worklistId }).unwrap()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
    setLoading(false)
  }

  const updateWorklist = async () => {
    setRouterBlocker(false)
    try {
      await putWorklistMutation({
        id: worklistId,
        data: worklistDetail.use_protocol ? {
          protocol_id: worklistDetail.use_protocol,
        } : {
          structure_set_label: worklistDetail.structure_set_label,
          description: worklistDetail.protocol_description,
          destination: worklistDetail.destination,
          structures: worklistDetail.structures.map(({ id, sort }) => ({ id, sort })),
          customized_structures: removeId(worklistDetail.customized_structures),
        },
      }).unwrap()
      setRouterConfig((prev) => ({ ...prev, state: true }))
    } catch (e) {
      setRouterBlocker(true)
      handleAlert({ title: i18n.t('error_titles.save'), content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const redraw = async () => {
    setRouterBlocker(false)
    try {
      await postWorklistRedrawMutation({
        worklist_id: worklistId,
        data: worklistDetail.use_protocol ? {
          protocol_id: worklistDetail.use_protocol,
        } : {
          structure_set_label: worklistDetail.structure_set_label,
          description: worklistDetail.protocol_description,
          destination: worklistDetail.destination,
          structures: worklistDetail.structures,
          customized_structures: removeId(worklistDetail.customized_structures),
        },
      }).unwrap()
      setRouterConfig({ state: true, url: '/' })
    } catch (e) {
      setRouterBlocker(true)
      handleAlert({ title: i18n.t('error_titles.draw'), content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const handleUpdateWorklistRemote = (_type: SourceDestinationKey, remote: RemoteCategoryKey, value: RemoteType[]) => {
    dispatch(updateWorklistRemote({ remote, value }))
  }

  const handleUpdateStructures = (structure: number[] | number) => {
    if (typeof structure === 'number') {
      dispatch(updateWorklistStructure({ id: structure }))
    } else {
      dispatch(updateWorklistDetail({ structures: structure.map((id) => ({ id })) }))
    }
  }
  const handleAddCustomizedStructures = (data: CustomizedStructuresType) => {
    dispatch(addWorklistCustomizedStructures({ data }))
  }
  const handleDeleteCustomizedStructures = (data: CustomizedStructuresType) => {
    dispatch(deleteWorklistCustomizedStructures({ data }))
  }
  const handleChangeCustomizedStructures = (
    id: number,
    fieldName: string,
    data: CustomizedStructuresType | CustomizedStructuresChangeDataType,
  ) => {
    dispatch(updateWorklistCustomizedStructures({ data: { id, [fieldName]: data } }))
  }

  const dataChecked = () => {
    let warning: string = ''

    const rules: { [key: string]: boolean } = {
      structure_set_label: !worklistDetail.structure_set_label,
      destination: !worklistDetail.destination.folder.length && !worklistDetail.destination.remote_server.length,
      structures: !worklistDetail.structures.length,
      customized_structures: false,
    }

    const rulesList = Object.entries(rules)
    if (rulesList.some((rule) => rule[1])) warning = generateErrorMessage(rulesList)

    const structureIdUnfilled = !worklistDetail.customized_structures.every((item) => item.name)
    const structureIdDuplicated = worklistDetail.customized_structures
      .some((col) => worklistDetail.customized_structures
        .some((item) => col.id !== item.id && col.name === item.name))
    if (structureIdUnfilled || structureIdDuplicated) {
      rules.customized_structures = true
      if (!warning) {
        warning = i18n.t(
          'modal_contents.wrong_with_customized_structures',
          { item: `"${i18n.t('titles.add_customized_structures')}"`, joinArrays: ' ' },
        )
      }
    }

    dispatch(updateConfigRequired({ rules }))
    setConformanceCheck(!warning)
    setModalContent(warning)
  }

  const handleOkSave = () => {
    if (updateWorklistDetailCheck) {
      updateWorklist()
    } else {
      navigate(routerConfig.url, { replace: true })
    }
  }

  const tabItems: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <span className={configRequired.destination ? 'red-ball' : ''}>
          {i18n.t('titles.destination')}
        </span>
      ),
      children: (
        <SendToDesination>
          <TransferTabs
            type="destination"
            checkList={worklistDetail}
            onUpdateRemote={handleUpdateWorklistRemote}
          />
        </SendToDesination>
      ),
    },
    {
      key: '2',
      label: (
        <span className={configRequired.structures ? 'red-ball' : ''}>
          {i18n.t('titles.structures')}
        </span>
      ),
      children: (
        <EditStructureLayout
          structureData={worklistDetail.structures.map((item) => item.id)}
          handleUpdateStructures={handleUpdateStructures}
        />
      ),
    },
    {
      key: '3',
      label: (
        <span className={configRequired.customized_structures.length ? 'red-ball' : ''}>
          {i18n.t('titles.add_customized_structures')}
        </span>
      ),
      children: (
        <EmptyStructureTable
          dataSource={worklistDetail.customized_structures}
          onAdd={handleAddCustomizedStructures}
          onChange={handleChangeCustomizedStructures}
          onDelete={handleDeleteCustomizedStructures}
        />
      ),
    },
  ]

  useEffect(() => {
    getApiData()
    return () => {
      dispatch(clearWorklistDetail())
      dispatch(resetConfigState())
    }
  }, [])

  useEffect(() => {
    setRouterBlocker(updateWorklistDetailCheck)
  }, [updateWorklistDetailCheck])

  useEffect(() => {
    if (routerConfig.state) navigate(routerConfig.url, { replace: true })
  }, [routerConfig])

  return (
    <>
      <ConfigLayoutHead navigatePage={navigatePage}>
        {i18n.t('titles.config_setting')}
      </ConfigLayoutHead>
      <Content>
        <ConfigLayout
          configData={worklistDetail}
          tabItems={tabItems}
          informationType="worklist"
        />
        <footer className="layout-footer" style={{ marginTop: 'auto' }}>
          <FooterBtnGroup
            items={draw ? [
              {
                btnName: i18n.t('buttons.draw'),
                modalTitle: conformanceCheck ? i18n.t('modal_titles.redraw_series') : '',
                modalContent: conformanceCheck ? i18n.t('modal_contents.redraw_series') : modalContent,
                modalFooter: conformanceCheck ? undefined : null,
                modalClassName: 'confirm-modal',
                modalBodyStyle: conformanceCheck ? {} : { fontSize: '18px', padding: '7.75rem 1.25rem 8.25rem' },
                btnClick() { dataChecked() },
                okText: i18n.t('buttons.draw'),
                onOk: redraw,
              },
            ] : [
              {
                type: 'button',
                btnName: i18n.t('buttons.cancel'),
                btnClick() { navigate('/', { replace: true }) },
              },
              {
                btnName: i18n.t('buttons.save'),
                modalTitle: conformanceCheck ? i18n.t('modal_titles.save_settings') : '',
                modalContent: conformanceCheck ? i18n.t('modal_contents.save_settings') : modalContent,
                modalFooter: conformanceCheck ? undefined : null,
                modalClassName: 'confirm-modal',
                modalBodyStyle: conformanceCheck ? {} : { fontSize: '18px', padding: '7.75rem 1.25rem 8.25rem' },
                btnClick() { dataChecked() },
                okText: i18n.t('buttons.save'),
                onOk: handleOkSave,
              },
            ]}
          />
        </footer>
      </Content>
      <Loading open={loading} />
      <LeaveConfirmModal condition={routerBlocker && updateWorklistDetailCheck} />
    </>
  )
}

export default ConfigSettingLayout
