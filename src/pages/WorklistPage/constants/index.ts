import { WorkStatusEnum } from 'src/utils/enum'

// Status options for active worklist
export const STATUS_OPTIONS: OptionType[] = [
  { value: WorkStatusEnum.PROCESSING, label: 'Processing' },
  { value: WorkStatusEnum.PENDING, label: 'Pending' },
  { value: WorkStatusEnum.SUSPENDED, label: 'Suspended' },
]

// Status options for history page
export const HISTORY_OPTIONS: OptionType[] = [
  { value: WorkStatusEnum.SUCCEEDED, label: 'Succeeded' },
  { value: WorkStatusEnum.FAILED, label: 'Failed' },
  { value: WorkStatusEnum.REMOVED, label: 'Removed' },
  { value: WorkStatusEnum.NO_MATCH, label: 'No Match' },
]
