import { useEffect } from 'react'

import { Form } from 'antd'

import {
  resetWorklistState,
  updateCurrentPage,
  updateWorklistFocus,
  updateWorklistGroupSearch,
  clearWorklist,
} from 'src/store/reducers/worklistSlice'

import type { SorterInfo } from '../types'
import { useTableState } from './useTableState'
import { useWorklistData } from './useWorklistData'
import { extractFormValues, calculateSortingParams } from '../utils/worklistHelpers'

/**
 * Custom hook for worklist operations and business logic
 */
export function useWorklistOperations(isHistory: boolean = false) {
  const [form] = Form.useForm()

  const {
    pathname,
    dispatch,
    handleAlert,
    worklistGroupFocus,
    worklistGroupPage,
    worklistGroupSearch,
    message,
    getWorklistGroupMutation,
    reGetWorklistGroupMutation,
    reGetWorklistMutation,
  } = useWorklistData()

  const {
    groupTableLoading,
    setGroupTableLoading,
    tableLoading,
    setTableLoading,
    groupSorted,
    setGroupSorted,
    worklistSorted,
    setWorklistSorted,
    historySorted,
    setHistorySorted,
  } = useTableState()

  const isHistoryPage = isHistory

  const getWorklistGroup = async (page?: number, sorter?: SorterInfo) => {
    setGroupTableLoading(true)

    try {
      const formValues = extractFormValues(form)
      const { nextPage, nextOrderKey, nextOrder } = calculateSortingParams(
        page,
        sorter,
        worklistGroupSearch,
      )

      await getWorklistGroupMutation({
        page: nextPage,
        patient_id: formValues.patientId,
        study_status: formValues.studyStatus,
        study_date_range_start: formValues.studyDateRangeStart,
        study_date_range_end: formValues.studyDateRangeEnd,
        order_key: nextOrderKey,
        ascend: nextOrder,
        history: isHistoryPage,
      }).unwrap()

      if (sorter) {
        setGroupSorted(sorter)
      }

      dispatch(updateCurrentPage({ page: nextPage }))
      dispatch(updateWorklistGroupSearch({
        patient_id: formValues.patientId,
        study_status: formValues.studyStatus,
        study_date_range_start: formValues.studyDateRangeStart,
        study_date_range_end: formValues.studyDateRangeEnd,
        order_key: nextOrderKey,
        ascend: nextOrder,
      }))
    } catch (error) {
      handleAlert({ content: (error as Err).data?.detail }, 'Msg', 'error')
    } finally {
      setGroupTableLoading(false)
    }
  }

  const reGetWorklistGroup = async () => {
    try {
      await reGetWorklistGroupMutation({
        page: worklistGroupPage.current,
        patient_id: worklistGroupSearch.patient_id,
        study_status: worklistGroupSearch.study_status,
        study_date_range_start: worklistGroupSearch.study_date_range_start,
        study_date_range_end: worklistGroupSearch.study_date_range_end,
        order_key: worklistGroupSearch.order_key,
        ascend: worklistGroupSearch.ascend,
        history: isHistoryPage,
      }).unwrap()
    } catch (error) {
      // Don't use useAlert for polling requests
      console.error('Failed to refresh worklist group:', error)
    }
  }

  const reGetWorklist = async (worklistGroupId: number) => {
    setTableLoading(true)

    try {
      await reGetWorklistMutation({ worklist_group_id: worklistGroupId }).unwrap()
    } catch (error) {
      handleAlert({ content: (error as Err).data?.detail }, 'Msg', 'error')
    } finally {
      setTableLoading(false)
    }
  }

  // Effects
  useEffect(() => {
    getWorklistGroup()

    return () => {
      form.resetFields()
      setGroupSorted({})
      setWorklistSorted({})
      setHistorySorted({})
      dispatch(updateWorklistFocus({ id: undefined }))
      dispatch(resetWorklistState())
    }
  }, [pathname])

  useEffect(() => {
    if (worklistGroupFocus.id) {
      reGetWorklist(worklistGroupFocus.id)
    } else {
      dispatch(clearWorklist())
    }
  }, [worklistGroupFocus.timeStamp])

  useEffect(() => {
    if (message.content === 'worklist_update') {
      reGetWorklistGroup()
    }
  }, [message.timestamp])

  return {
    form,
    isHistoryPage,
    groupTableLoading,
    tableLoading,
    groupSorted,
    worklistSorted,
    setWorklistSorted,
    historySorted,
    setHistorySorted,
    getWorklistGroup,
  }
}
