import { useState } from 'react'

import type { SorterResult } from 'antd/es/table/interface'

/**
 * Custom hook for managing table state (loading and sorting)
 */
export function useTableState() {
  const [groupTableLoading, setGroupTableLoading] = useState<boolean>(false)
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [groupSorted, setGroupSorted] = useState<SorterResult<WorklistGroupType>>({})
  const [worklistSorted, setWorklistSorted] = useState<SorterResult<SeriesType>>({})
  const [historySorted, setHistorySorted] = useState<SorterResult<HistoryWorklistType>>({})

  return {
    groupTableLoading,
    setGroupTableLoading,
    tableLoading,
    setTableLoading,
    groupSorted,
    setGroupSorted,
    worklistSorted,
    setWorklistSorted,
    historySorted,
    setHistorySorted,
  }
}
