import type { FormInstance, SorterInfo } from '../types'

/**
 * Extract and format form values for API requests
 */
export function extractFormValues(form: FormInstance) {
  const formValues = form.getFieldsValue()
  return {
    patientId: formValues.patientId,
    studyStatus: formValues.studyStatus,
    studyDateRangeStart: formValues.pickDate?.[0]?.format('YYYY-MM-DD HH:00'),
    studyDateRangeEnd: formValues.pickDate?.[1]?.format('YYYY-MM-DD HH:00'),
  }
}

/**
 * Calculate sorting parameters for API requests
 */
export function calculateSortingParams(
  page: number | undefined,
  sorter: SorterInfo | undefined,
  currentSearch: {
    order_key: string
    ascend: boolean
  },
) {
  const nextPage = page || 1
  let nextOrderKey = ''

  // Page undefined means changing sorter, order undefined means cancel the sorter
  if (!page) {
    nextOrderKey = sorter && sorter.order ? sorter.field || '' : ''
  } else {
    nextOrderKey = currentSearch.order_key
  }

  const nextOrder = sorter ? sorter.order === 'ascend' : currentSearch.ascend

  return { nextPage, nextOrderKey, nextOrder }
}
