import 'src/styles/components/tableCascader.css'

import IndexTableLayout from 'src/Layout/IndexTableLayout'

import { STATUS_OPTIONS, HISTORY_OPTIONS } from './constants'
import { useWorklistOperations } from './hooks/useWorklistOperations'

/**
 * Main Worklist page component that handles both active worklist and history
 */
function WorklistPage() {
  const {
    form,
    isHistoryPage,
    groupTableLoading,
    tableLoading,
    groupSorted,
    worklistSorted,
    setWorklistSorted,
    setHistorySorted,
    getWorklistGroup,
  } = useWorklistOperations()

  // Computed values
  const pageTitle = isHistoryPage ? 'titles.history' : 'titles.task_list'
  const searchOptions = isHistoryPage ? HISTORY_OPTIONS : STATUS_OPTIONS

  return (
    <IndexTableLayout
      isHistoryPage={isHistoryPage}
      pageTitle={pageTitle}
      searchOptions={searchOptions}
      form={form}
      groupTableLoading={groupTableLoading}
      tableLoading={tableLoading}
      groupSorted={groupSorted}
      worklistSorted={worklistSorted}
      setWorklistSorted={setWorklistSorted}
      setHistorySorted={setHistorySorted}
      handleSearch={getWorklistGroup}
    />
  )
}

export default WorklistPage
