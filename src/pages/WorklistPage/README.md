# WorklistPage

This directory contains the refactored Worklist page implementation with a clean separation of concerns.

## Architecture

```
src/pages/WorklistPage/
├── README.md                    # This file
├── index.ts                     # Main export file
├── WorklistPage.tsx             # Main page component
├── constants/
│   └── index.ts                 # Status options constants
├── hooks/
│   ├── useWorklistData.ts       # Data fetching and state management
│   ├── useTableState.ts         # Table state management
│   └── useWorklistOperations.ts # Business logic and operations
├── types/
│   └── index.ts                 # TypeScript type definitions
└── utils/
    └── worklistHelpers.ts       # Pure utility functions
```

## Components

### WorklistPage.tsx
The main page component that orchestrates all the hooks and passes data to the layout component.

### IndexTableLayout (src/Layout/IndexTableLayout.tsx)
Pure UI layout component that receives all props and renders the table interface.

## Hooks

### useWorklistData()
Manages all data-related operations:
- Redux state selectors
- API mutation hooks
- WebSocket message handling
- Alert handling

### useTableState()
Manages table-specific state:
- Loading states
- Sorting states

### useWorklistOperations()
Contains all business logic:
- Form operations
- API calls
- Effect handlers
- Data transformations

## Types

All TypeScript interfaces are defined in `types/index.ts`:
- `SearchFormValues` - Form data structure
- `SorterInfo` - Table sorting information
- `FormInstance` - Form instance interface

## Constants

Status options for both active worklist and history pages are defined in `constants/index.ts`.

## Utilities

Pure functions for data transformation:
- `extractFormValues()` - Extract and format form data
- `calculateSortingParams()` - Calculate sorting parameters

## Usage

```typescript
import WorklistPage from 'src/pages/WorklistPage'

// Use in routing
<Route path="/" element={<WorklistPage />} />
<Route path="history" element={<WorklistPage />} />
```

## Benefits

1. **Separation of Concerns**: UI, business logic, and data management are clearly separated
2. **Reusability**: Hooks can be reused in other components
3. **Testability**: Pure functions and isolated hooks are easy to test
4. **Maintainability**: Clear structure makes code easy to understand and modify
5. **Type Safety**: Complete TypeScript coverage with proper interfaces
