import { useEffect } from 'react'

import { Collapse, ConfigProvider } from 'antd'

import { DownIcon } from 'src/assets/icons'
import useParseFAQ from 'src/hook/useParseFAQ'
import i18n from 'src/i18n'
import { color } from 'src/styles/utils/variables'

function FAQ() {
  const dataItems = useParseFAQ()

  useEffect(() => {
    window.scrollTo({
      top: 0,
      left: 0,
    })
  }, [])

  return (
    <section id="FAQ-wrapper">
      <hgroup className="FAQ-title">
        <h2>FAQ</h2>
        <p>
          {i18n.t('FAQ.detail', { joinArrays: '\r\n' })}
        </p>
      </hgroup>
      <section className="FAQ-content">
        <ConfigProvider theme={{
          components: {
            Collapse: {
              contentBg: color.gray[3],
              headerBg: color.gray[2],
              headerPadding: '.5rem 1rem',
              contentPadding: '1rem 2rem',
              colorText: color.gray[1].default,
              colorBorder: 'var(--manual-border-color)',
              lineHeight: 2.25,
            },
          },
        }}
        >
          {dataItems?.map((item) => (
            <Collapse
              key={item.key}
              bordered
              collapsible="header"
              items={[item]}
              // eslint-disable-next-line react/no-unstable-nested-components
              expandIcon={({ isActive }) => (
                <div className="faq-icon">
                  <DownIcon className={isActive ? 'rotate-0' : 'rotate-90'} />
                </div>
              )}
              style={{
                marginBottom: 24,
              }}
            />
          ))}
        </ConfigProvider>
      </section>
    </section>
  )
}

export default FAQ
