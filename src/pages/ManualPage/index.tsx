import { useRef } from 'react'

import { ConfigProvider, Layout } from 'antd'
import { Outlet, useLocation } from 'react-router'

import { Head } from 'src/components/Head'
import usePreventPageActions from 'src/hook/usePreventPageActions'
import i18n from 'src/i18n'
import { color } from 'src/styles/utils/variables'
import type { MenuItems } from 'src/typings/menu.type'

import Sidebar from './Sidebar'

import 'src/styles/pages/manual.css'

const { Content } = Layout

type Props = {
  menu: MenuItems
}

function ManualPage({ menu }: Props) {
  const headRef = useRef<HTMLElement>(null)
  const contentRef = useRef<HTMLElement>(null)
  const { pathname } = useLocation()
  usePreventPageActions()

  return (
    <ConfigProvider
      theme={{
        components: {
          Layout: {
            headerBg: color.gray[4],
            bodyBg: color.gray[3],
            siderBg: color.gray[4],
          },
        },
      }}
    >
      <Head classNames={{ header: 'manual-wrapper-header' }} ref={headRef}>
        {i18n.t(`titles.${pathname.split('/')[1]}`)}
      </Head>
      <Layout
        onCopy={() => false}
        id="manual-wrapper-layout"
        className="print-none "
        style={{
          height: '100%',
          maxHeight: 'calc(100vh - 64px)',
          minHeight: 'auto',
          borderRadius: 8,
          overflow: 'auto',
        }}
      >
        <Sidebar
          isFAQ={pathname === '/manual'}
          anchorProps={{
            items: menu.items,
            getContainer: () => contentRef.current || window,
          }}
        />
        <Content
          ref={contentRef}
          style={{
            padding: 0,
            overflow: 'auto',
            background: color.gray[3],
            fontSize: '1rem',
            color: color.gray[1].default,
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </ConfigProvider>
  )
}

export default ManualPage
