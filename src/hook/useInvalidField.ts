import { useAppDispatch, useAppSelector } from 'src/store/hook'
import { ConfigRequiredType, updateConfigRequired } from 'src/store/reducers/configSlice'
import { generateErrorMessage, GenerateErrorMessageParams } from 'src/utils/helper'
import { verifyDetailState } from 'src/utils/verify'

import useAlert from './useAlert'

const useInvalidField = (detail: WorklistDetailType | ProtocolDetailType) => {
  // redux
  const dispatch = useAppDispatch()
  const { structureConfig } = useAppSelector((state) => state.configReducer)
  // hooks
  const handleAlert = useAlert()

  const structureIds = detail.structures.map((item) => item.id)

  const verifyDetail = async () => {
    const rules: Partial<ConfigRequiredType> = {
      protocol_name: 'protocol_name' in detail ? verifyDetailState.required(detail.protocol_name) : false,
      study_info: 'study_info' in detail ? verifyDetailState.studyInfo(detail.study_info) : false,
      structure_set_label: verifyDetailState.structure_set_label(detail.structure_set_label),
      structures: !detail.structures.length,
      source: 'source' in detail ? verifyDetailState.remote(detail.source) : false,
      destination: verifyDetailState.destination(detail.destination),
      customized_structures: verifyDetailState.customizedStructures({
        custom: detail.customized_structures,
        origin: structureConfig,
      }, structureIds),
    }

    const invalidFields = Object
      .values(rules)
      .filter((value) => (Array.isArray(value) ? value.length : value))

    dispatch(updateConfigRequired({ rules }))

    if (invalidFields.length === 0) return

    //  handle error
    const content = generateErrorMessage(Object.entries(rules) as GenerateErrorMessageParams[])

    handleAlert({
      content,
      closable: true,
      footer: null,
      centered: true,
    }, 'Modal', 'confirm')
    throw new Error('throw new Error')
  }

  return { verifyDetail }
}

export default useInvalidField
