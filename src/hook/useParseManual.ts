import { useEffect, useState } from 'react'

import { fetchManualExcel } from 'src/utils/fetchExcelData'
import { parseElements, parseMenu } from 'src/utils/parseManual'

import type { MenuItems } from '../typings/menu.type'

type Hook = () => [BaseElement[], MenuItems]

const useParseManual: Hook = () => {
  const [jsonData, setJsonData] = useState<BaseElement[]>([])
  const [menuList, setMenuList] = useState<MenuItems>({ items: [], defaultOpenKeys: [] })

  useEffect(() => {
    (async function fn() {
      const data = await fetchManualExcel('direction')
      const elements: BaseElement[] = await parseElements(data)
      const menu = await parseMenu(elements)
      setJsonData(elements)
      setMenuList(menu)
    }())
  }, [])

  return [jsonData, menuList]
}

export default useParseManual
