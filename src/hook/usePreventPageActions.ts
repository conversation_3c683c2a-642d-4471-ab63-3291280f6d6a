import { useEffect } from 'react'

const usePreventPageActions = () => {
  const handleKeydown = (e: KeyboardEvent) => {
    const isCtrlOrMetaKey = e.ctrlKey || e.metaKey
    // copy = c, save = s, open = o, bookmark = d, print = p
    const keysToPrevent = new Set(['c', 's', 'o', 'd', 'p'])
    const simpleKey = keysToPrevent.has(e.key) && isCtrlOrMetaKey

    // mac devtool shortcuts
    const openElementKey = e.code === 'KeyC' && e.metaKey && e.altKey
    const openConsoleKey = e.code === 'KeyJ' && e.metaKey && e.altKey
    const openDevToolWithMac = e.code === 'KeyI' && e.metaKey && e.altKey
    const sourceHtmlKeyWithMac = e.code === 'KeyU' && e.metaKey && e.altKey
    const devToolAllKeyWithMac = openElementKey || openConsoleKey || openDevToolWithMac || sourceHtmlKeyWithMac
    // windows devtool shortcuts
    const sourceHtmlKeyWithWindow = e.key === 'u' && e.ctrlKey
    const openDevToolWithWindow = e.code === 'KeyJ' && e.ctrlKey && e.shiftKey
    const devToolAllkeyWithWindow = openDevToolWithWindow || sourceHtmlKeyWithWindow
    // open console
    const consoleKeyWithF12 = e.key === 'F12'
    const consoleKey = consoleKeyWithF12 || devToolAllKeyWithMac || devToolAllkeyWithWindow
    // all shortcuts
    const preventShortcuts = simpleKey || consoleKey

    if (preventShortcuts) e.preventDefault()
  }

  const handleContentMenu = (e: MouseEvent) => e.preventDefault()

  useEffect(() => {
    document.addEventListener('keydown', handleKeydown)
    document.addEventListener('contextmenu', handleContentMenu)

    return () => {
      document.removeEventListener('keydown', handleKeydown)
      document.removeEventListener('contextmenu', handleContentMenu)
    }
  }, [])
}

export default usePreventPageActions
