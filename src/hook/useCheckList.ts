import {
  useReducer, useCallback, useEffect, useRef,
} from 'react'

// Define the item interface
interface Item {
  id: string | number;
  checked?: boolean;
}

// Define action types for the reducer
type Action<T extends Item> =
  | { type: 'SET_LIST'; payload: T[] }
  | { type: 'UPDATE_ITEM'; payload: { id: T['id']; checked: boolean } }
  | { type: 'TOGGLE_ITEM'; payload: { id: T['id'] } }
  | { type: 'TOGGLE_ALL' }
  | { type: 'UPDATE_ALL'; payload: { checked: boolean } }

// Reducer function to update list state based on action type
function listReducer<T extends Item>(state: T[], action: Action<T>): T[] {
  switch (action.type) {
    case 'SET_LIST':
      return action.payload
    case 'UPDATE_ITEM': {
      const { id, checked } = action.payload
      // Update the checked state for the specific item
      return state.map((item) => (item.id === id ? { ...item, checked } : item))
    }
    case 'TOGGLE_ITEM': {
      const { id } = action.payload
      // Toggle the checked state for the specific item
      return state.map((item) => (item.id === id ? { ...item, checked: !item.checked } : item))
    }
    case 'TOGGLE_ALL': {
      // Determine if all items are currently checked
      const allChecked = state.every((item) => item.checked)
      // Toggle all items to the opposite of current overall state
      return state.map((item) => ({ ...item, checked: !allChecked }))
    }
    case 'UPDATE_ALL': {
      const { checked } = action.payload
      // Update all items to the given checked state
      return state.map((item) => ({ ...item, checked }))
    }
    default:
      return state
  }
}

// Shallow compare two arrays of items based on id and checked
function shallowCompareItems<T extends Item>(a: T[], b: T[]): boolean {
  if (a.length !== b.length) return false
  for (let i = 0; i < a.length; i++) {
    if (a[i].id !== b[i].id || a[i].checked !== b[i].checked) {
      return false
    }
  }
  return true
}

export function useCheckList<T extends Item>(initialList: T[] = []) {
  // Use a ref to store previous initialList
  const initialListRef = useRef(initialList)
  const [list, dispatch] = useReducer(listReducer, initialList)

  // update list when initialList change
  useEffect(() => {
    if (!shallowCompareItems(initialListRef.current, initialList)) {
      initialListRef.current = initialList
      dispatch({ type: 'SET_LIST', payload: initialList })
    }
  }, [initialList])

  // Find an item by id
  const findItem = useCallback((id: T['id']) => list.find((item) => `${item.id}` === `${id}`), [list, initialList])

  // Check if an item is checked
  const isChecked = useCallback((id: T['id']) => {
    return findItem(id)?.checked
  }, [findItem, list])

  // Check if all items are checked
  const isAllChecked = useCallback(() => list.every((item) => item.checked), [list])

  // Set the entire list state and return new list
  const setList = useCallback((items: T[]) => {
    dispatch({ type: 'SET_LIST', payload: items })
    return items
  }, [])

  // Update a specific item's checked state and return new list
  const updateItem = useCallback((id: T['id'], checked: boolean) => {
    const newList = listReducer(list, { type: 'UPDATE_ITEM', payload: { id, checked } })
    dispatch({ type: 'UPDATE_ITEM', payload: { id, checked } })
    return newList
  }, [list])

  // Toggle a specific item's checked state and return new list
  const toggle = useCallback((id: T['id']) => {
    const newList = listReducer(list, { type: 'TOGGLE_ITEM', payload: { id } })
    dispatch({ type: 'TOGGLE_ITEM', payload: { id } })
    return newList
  }, [list])

  // Set a specific item as checked and return new list
  const check = useCallback((id: T['id']) => {
    const newList = listReducer(list, { type: 'UPDATE_ITEM', payload: { id, checked: true } })
    dispatch({ type: 'UPDATE_ITEM', payload: { id, checked: true } })
    return newList
  }, [list])

  // Set a specific item as unchecked and return new list
  const unCheck = useCallback((id: T['id']) => {
    const newList = listReducer(list, { type: 'UPDATE_ITEM', payload: { id, checked: false } })
    dispatch({ type: 'UPDATE_ITEM', payload: { id, checked: false } })
    return newList
  }, [list])

  // Toggle all items' checked state and return new list
  const toggleAll = useCallback(() => {
    const newList = listReducer(list, { type: 'TOGGLE_ALL' })
    dispatch({ type: 'TOGGLE_ALL' })
    return newList
  }, [list])

  // Check all items and return new list
  const checkAll = useCallback(() => {
    const newList = listReducer(list, { type: 'UPDATE_ALL', payload: { checked: true } })
    dispatch({ type: 'UPDATE_ALL', payload: { checked: true } })
    return newList
  }, [list])

  // Uncheck all items and return new list
  const unCheckAll = useCallback(() => {
    const newList = listReducer(list, { type: 'UPDATE_ALL', payload: { checked: false } })
    dispatch({ type: 'UPDATE_ALL', payload: { checked: false } })
    return newList
  }, [list])

  // Update all items to the given checked state and return new list
  const updateAll = useCallback((checked: boolean) => {
    const newList = listReducer(list, { type: 'UPDATE_ALL', payload: { checked } })
    dispatch({ type: 'UPDATE_ALL', payload: { checked } })
    return newList
  }, [list])

  // Get list of all checked items
  const getCheckedList = useCallback(() => {
    return list.filter((item) => item.checked)
  }, [list])

  // Get list of all checked item ids
  const getCheckedIds = useCallback(() => {
    return list.filter((item) => item.checked).map((item) => item.id)
  }, [list])

  return {
    list,
    setList,
    isChecked,
    isAllChecked,
    check,
    unCheck,
    toggle,
    updateItem,
    toggleAll,
    checkAll,
    unCheckAll,
    updateAll,
    getCheckedList,
    getCheckedIds,
  }
}
