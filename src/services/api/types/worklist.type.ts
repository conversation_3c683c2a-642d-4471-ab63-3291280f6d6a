import type {
  PaginatedR<PERSON>ponse, <PERSON><PERSON><PERSON><PERSON><PERSON>, Date<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>d<PERSON><PERSON><PERSON>, <PERSON>UpdateParam,
} from './common.type'

// Get worklist group request parameters
export interface GetWorklistGroupRequest extends Sort<PERSON>ara<PERSON>, DateRangeFilter {
  page?: number
  patient_id?: string
  study_status?: string
  history?: boolean
}

// Get worklist group response data
export interface GetWorklistGroupResponse extends PaginatedResponse<WorklistGroupType> {
  worklist_group: WorklistGroupType[]
}

// Prioritize worklist group request parameters
export interface PrioritizeWorklistGroupRequest {
  worklist_group_id: number
}

// Update worklist group status request parameters
export interface UpdateWorklistGroupStatusRequest {
  worklist_group_id: number
  status: string
}

// Get worklist request parameters
export interface GetWorklistRequest extends SortParams {
  worklist_group_id: number
}

// Get worklist response data
export interface GetWorklistResponse extends WorklistType { }

// Update worklist status request parameters
export interface UpdateWorklistStatusRequest extends StatusUpdateParam { }

// Get worklist detail request parameters
export interface GetWorklistDetailRequest extends Id<PERSON>aram { }

// Get worklist detail response data
export interface GetWorklistDetailResponse extends WorklistDetailType { }

// Update worklist request parameters
export interface UpdateWorklistRequest extends IdParam {
  data: DetailTypeWithAPI
}

// Redraw worklist request parameters
export interface RedrawWorklistRequest {
  worklist_id: number
  data: DetailTypeWithAPI
}

// RS resend request parameters
export interface RsResendRequest extends IdParam {
  remote_server_id_list: RemoteType[]
  folder_id_list: RemoteType[]
}

// Get RS preview request parameters
export interface GetRsPreviewRequest {
  worklist_id: number | string
}
