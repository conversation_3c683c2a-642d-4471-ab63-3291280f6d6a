// Standard API response wrapper
export interface ApiResponse<T = unknown> {
  result: string
  data?: T
}

// Base response for simple operations
export interface BaseResponse {
  result: string
}

// Pagination metadata
export interface PaginationMeta {
  current_page: number
  page_size: number
  total_data: number
}

// Paginated response wrapper
export interface PaginatedResponse<T> extends PaginationMeta {
  data: T[]
}

// Sort parameters
export interface SortParams {
  order_key?: string
  ascend?: boolean
}

// Date range filter
export interface DateRangeFilter {
  study_date_range_start?: string
  study_date_range_end?: string
}

// Common ID parameter
export interface IdParam {
  id: number
}

// Common status update parameter
export interface StatusUpdateParam extends IdParam {
  status: string
}

// File download parameters
export interface DownloadParams {
  rs?: boolean
  ct?: boolean
}
