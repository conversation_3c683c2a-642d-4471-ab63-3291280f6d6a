// Get notifications request parameters
export interface GetNotifyRequest {
  quantity: number //  Number of notifications to retrieve
}

// Get notifications response data
export interface GetNotifyResponse {
  total_notify: number //  Total number of notifications
  notify_list: NotifyType[] //  List of notification items
}

// Delete notification request parameters
export interface DeleteNotifyRequest {
  notify_id?: number //  ID of notification to delete (optional for bulk delete)
}
