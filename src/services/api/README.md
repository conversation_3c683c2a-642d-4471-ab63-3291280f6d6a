# API Library

Modern, type-safe API layer built with RTK Query for the RT-HCAPSeg application.

## Features

- **Type-safe**: Full TypeScript support with strict typing
- **Modular**: Organized by feature domains (auth, worklist, protocol, etc.)
- **Error handling**: Unified error handling with proper type guards
- **Helper utilities**: Common functions for parameter building and file downloads
- **RTK Query**: Built on Redux Toolkit Query for efficient data fetching

## Structure

```
src/lib/api/
├── index.ts              # Main API configuration
├── base-query.ts         # Base query setup
├── helpers.ts            # Utility functions
├── error-handler.ts      # Error handling utilities
├── endpoints/            # Feature-specific endpoints
│   ├── auth.ts           # Authentication
│   ├── notify.ts         # Notifications
│   ├── worklist.ts       # Worklist management
│   ├── protocol.ts       # Protocol management
│   ├── structure.ts      # Structure management
│   ├── remote.ts         # Remote server management
│   └── download.ts       # File downloads
└── types/                # Type definitions
    ├── common.ts         # Shared types
    ├── auth.ts           # Authentication types
    ├── notify.ts         # Notification types
    └── worklist.ts       # Worklist types
```

## Usage

### Basic Example

```typescript
import {
  usePostLoginMutation,
  useGetWorklistGroupMutation
} from 'src/lib/api'

function LoginComponent() {
  const [login, { isLoading }] = usePostLoginMutation()
  const [getWorklistGroup] = useGetWorklistGroupMutation()

  const handleLogin = async (credentials) => {
    try {
      const result = await login(credentials).unwrap()
      console.log('Login successful:', result.user_name)
    } catch (error) {
      console.error('Login failed:', error)
    }
  }

  return (
    <button onClick={() => handleLogin({ account: 'user', password: 'pass' })}>
      {isLoading ? 'Logging in...' : 'Login'}
    </button>
  )
}
```

### Error Handling

```typescript
import { handleApiError } from 'src/lib/api/error-handler'

try {
  await login({ account: 'user', password: 'pass' }).unwrap()
} catch (error) {
  const message = handleApiError(error, 'Login')
  showErrorToast(message)
}
```

### Helper Functions

```typescript
import { buildParams, createApiQuery } from 'src/services/api/helpers'

// Filter out undefined values
const cleanParams = buildParams({
  page: 1,
  filter: undefined,
  sort: 'name'
})
// Result: { page: 1, sort: 'name' }

// Create standardized API query
const query = createApiQuery({
  url: '/users',
  method: 'GET',
  params: { page: 1 }
})
```

## Available Endpoints

### Authentication
- `usePostLoginMutation` - User login
- `useDeleteLoginMutation` - User logout

### Notifications
- `useGetNotifyMutation` - Get notifications
- `useReGetNotifyMutation` - Refresh notifications
- `useDeleteNotifyMutation` - Delete notification

### Worklist
- `useGetWorklistGroupMutation` - Get worklist groups
- `useGetWorklistMutation` - Get worklist items
- `useGetWorklistDetailMutation` - Get worklist details
- `usePutWorklistMutation` - Update worklist
- `usePostWorklistRedrawMutation` - Redraw worklist

### Protocol
- `useLazyGetProtocolQuery` - Get protocols (cached)
- `useGetProtocolDetailMutation` - Get protocol details
- `usePostProtocolMutation` - Create protocol
- `usePutProtocolMutation` - Update protocol
- `useDeleteProtocolMutation` - Delete protocol

### Structure
- `useGetStructureMutation` - Get structures
- `usePutStructureMutation` - Update structures
- `useGetStructureConfigMutation` - Get structure config

### Remote
- `useGetRemoteMutation` - Get remote servers/folders
- `usePutRemoteMutation` - Update remote config
- `usePostRemoteServerMutation` - Add remote server
- `useDeleteRemoteServerMutation` - Remove remote server

### Downloads
- `useGetDicomMutation` - Download DICOM files
- `useGetRsPreviewZipMutation` - Get RS preview images

## Type Definitions

All types are properly exported and can be imported:

```typescript
import type {
  LoginRequest,
  LoginResponse,
  GetWorklistGroupRequest,
  ApiError
} from 'src/lib/api/types'
```

## Development

When adding new endpoints:

1. Create types in the appropriate `types/` file
2. Add endpoint to the relevant `endpoints/` file
3. Export the hook from `index.ts`
4. Update this README with the new endpoint

## Migration Notes

This API layer replaces the legacy `src/services/api.ts`. The old API is still available for backward compatibility but is deprecated.
