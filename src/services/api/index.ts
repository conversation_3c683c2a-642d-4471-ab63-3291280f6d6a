/**
 * Main API configuration using RTK Query
 */
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

import { baseQueryWithReauth } from './base-query'
import { createAuthEndpoints } from './endpoints/auth.api'
import { createDownloadEndpoints } from './endpoints/download.api'
import { createNotifyEndpoints } from './endpoints/notify.api'
import { createProtocolEndpoints } from './endpoints/protocol.api'
import { createRemoteEndpoints } from './endpoints/remote.api'
import { createStructureEndpoints } from './endpoints/structure.api'
import { createWorklistEndpoints } from './endpoints/worklist.api'
import type { RootState } from '../../store/index'

export const api = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth(fetchBaseQuery({
    baseUrl: window.config.env.api.rest,
    prepareHeaders: (headers, { getState }) => {
      const { token } = (getState() as RootState).authReducer

      if (token) {
        headers.set('authorization', `Bearer ${token}`)
      }

      // Security headers
      headers.set('X-Frame-Options', 'DENY')
      headers.set('Content-Security-Policy', 'frame-ancestors \'none\'')

      return headers
    },
  })),
  endpoints: (builder) => ({
    ...createAuthEndpoints(builder), // Authentication endpoints
    ...createNotifyEndpoints(builder), // Notification endpoints
    ...createWorklistEndpoints(builder), // Worklist endpoints
    ...createProtocolEndpoints(builder), // Protocol endpoints
    ...createStructureEndpoints(builder), // Structure endpoints
    ...createRemoteEndpoints(builder), // Remote endpoints
    ...createDownloadEndpoints(builder), // Download endpoints
  }),
})

// Export hooks for components
export const {
  // Auth
  usePostLoginMutation,
  useDeleteLoginMutation,

  // Notify
  useGetNotifyMutation,
  useReGetNotifyMutation,
  useDeleteNotifyMutation,

  // Worklist
  useGetWorklistGroupMutation,
  useReGetWorklistGroupMutation,
  usePatchPrioritizeWorklistGroupMutation,
  usePatchWorklistGroupMutation,
  useGetWorklistMutation,
  useReGetWorklistMutation,
  usePatchWorklistStatusMutation,
  useGetWorklistDetailMutation,
  usePutWorklistMutation,
  usePostWorklistRedrawMutation,
  usePostRsResendMutation,
  useGetRsPreviewZipMutation,

  // Protocol
  useLazyGetProtocolQuery,
  useGetProtocolConfigMutation,
  useGetProtocolDetailMutation,
  useGetWorklistProtocolDetailMutation,
  useCheckProtocolNameMutation,
  usePostProtocolMutation,
  usePutProtocolMutation,
  useDeleteProtocolMutation,
  usePutProtocolSortMutation,
  usePatchProtocolStatusMutation,

  // Structure
  useGetStructureMutation,
  usePutStructureMutation,
  useGetStructureConfigMutation,

  // Remote
  useGetRemoteMutation,
  useGetRemoteStatusMutation,
  useGetRemoteConfigMutation,
  usePutRemoteMutation,
  usePostRemoteServerMutation,
  useDeleteRemoteServerMutation,
  usePostFolderMutation,
  useDeleteFolderMutation,

  // Download
  useGetDicomMutation,
} = api
