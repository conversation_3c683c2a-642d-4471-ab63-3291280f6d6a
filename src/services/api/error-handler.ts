/**
 * API error handling utilities
 */

/**
 * Standard API error interface
 */
export interface ApiError {
  status: number
  data: {
    detail?: string
    message?: string
  }
}

/**
 * Type guard to check if error is an API error
 * @param error - Unknown error object
 * @returns True if error matches ApiError interface
 */
export function isApiError(error: unknown): error is ApiError {
  return (
    typeof error === 'object'
    && error !== null
    && 'status' in error
    && 'data' in error
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    && typeof (error as any).status === 'number'
  )
}

/**
 * Extract error message from API error
 * @param error - Error object
 * @returns Human-readable error message
 */
export function getErrorMessage(error: unknown): string {
  if (isApiError(error)) {
    return error.data.detail || error.data.message || 'Unknown API error'
  }

  if (error instanceof Error) {
    return error.message
  }

  return 'Network error occurred'
}

/**
 * Handle API error with consistent logging
 * @param error - Error object
 * @param context - Context where error occurred
 * @returns Error message
 */
export function handleApiError(error: unknown, context?: string): string {
  const message = getErrorMessage(error)

  if (context) {
    console.error(`[${context}] API Error:`, error)
  } else {
    console.error('API Error:', error)
  }

  return message
}

/**
 * Create error handler for RTK Query mutations
 * @param context - Context for error logging
 * @returns Error handler function
 */
export function createErrorHandler(context: string) {
  return (error: unknown) => handleApiError(error, context)
}
