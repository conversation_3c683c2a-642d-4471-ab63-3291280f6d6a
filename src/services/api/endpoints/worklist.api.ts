import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'
import J<PERSON>Zip from 'jszip'

import { createApiQuery } from '../helpers'
import type { BaseResponse } from '../types/common.type'
import type {
  GetWorklistGroupRequest,
  GetWorklistGroupResponse,
  PrioritizeWorklistGroupRequest,
  UpdateWorklistGroupStatusRequest,
  GetWorklistRequest,
  GetWorklistResponse,
  UpdateWorklistStatusRequest,
  GetWorklistDetailRequest,
  GetWorklistDetailResponse,
  UpdateWorklistRequest,
  RedrawWorklistRequest,
  RsResendRequest,
  GetRsPreviewRequest,
} from '../types/worklist.type'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function createWorklistEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get worklist groups with token refresh
    getWorklistGroup: builder.mutation<GetWorklistGroupResponse, GetWorklistGroupRequest>({
      query: (params: GetWorklistGroupRequest) => createApiQuery({
        url: '/worklist/group',
        method: 'GET',
        params,
      }),
    }),

    // Get worklist groups without token refresh (for polling)
    reGetWorklistGroup: builder.mutation<GetWorklistGroupResponse, GetWorklistGroupRequest>({
      query: (params: GetWorklistGroupRequest) => createApiQuery({
        url: '/worklist/group',
        method: 'GET',
        params,
      }),
    }),

    // Prioritize worklist group
    patchPrioritizeWorklistGroup: builder.mutation<BaseResponse, PrioritizeWorklistGroupRequest>({
      query: ({ worklist_group_id }: PrioritizeWorklistGroupRequest) => {
        return createApiQuery({
          url: `/worklist/group/${worklist_group_id}/insert`,
          method: 'PATCH',
        })
      },
    }),

    // Update worklist group status
    patchWorklistGroup: builder.mutation<BaseResponse, UpdateWorklistGroupStatusRequest>({
      query: ({ worklist_group_id, status }: UpdateWorklistGroupStatusRequest) => {
        return createApiQuery({
          url: `/worklist/group/${worklist_group_id}/status`,
          method: 'PATCH',
          body: { status },
        })
      },
    }),

    // Get worklist with token refresh
    getWorklist: builder.mutation<GetWorklistResponse, GetWorklistRequest>({
      query: ({ worklist_group_id, ...params }: GetWorklistRequest) => {
        return createApiQuery({
          url: `/worklist/${worklist_group_id}`,
          method: 'GET',
          params,
        })
      },
    }),

    // Get worklist without token refresh (for polling)
    reGetWorklist: builder.mutation<GetWorklistResponse, GetWorklistRequest>({
      query: ({ worklist_group_id, ...params }: GetWorklistRequest) => {
        return createApiQuery({
          url: `/worklist/${worklist_group_id}`,
          method: 'GET',
          params,
        })
      },
    }),

    // Update worklist status
    patchWorklistStatus: builder.mutation<BaseResponse, UpdateWorklistStatusRequest>({
      query: ({ id, status }: UpdateWorklistStatusRequest) => {
        return createApiQuery({
          url: `/worklist/${id}/status`,
          method: 'PATCH',
          body: { status },
        })
      },
    }),

    // Get worklist detail
    getWorklistDetail: builder.mutation<GetWorklistDetailResponse, GetWorklistDetailRequest>({
      query: ({ id }: GetWorklistDetailRequest) => createApiQuery({
        url: `/worklist/${id}/detail`,
        method: 'GET',
      }),
    }),

    // Update worklist
    putWorklist: builder.mutation<BaseResponse, UpdateWorklistRequest>({
      query: ({ id, data }: UpdateWorklistRequest) => createApiQuery({
        url: `/worklist/${id}/update`,
        method: 'PUT',
        body: data,
      }),
    }),

    // Redraw worklist
    postWorklistRedraw: builder.mutation<BaseResponse, RedrawWorklistRequest>({
      query: ({ worklist_id, data }: RedrawWorklistRequest) => {
        return createApiQuery({
          url: `/task/${worklist_id}/redraw`,
          method: 'POST',
          body: data,
        })
      },
    }),

    // Resend RS DICOM
    postRsResend: builder.mutation<BaseResponse, RsResendRequest>({
      query: ({ id, remote_server_id_list, folder_id_list }: RsResendRequest) => {
        return createApiQuery({
          url: `/task/${id}/rs/resend`,
          method: 'POST',
          body: {
            remote_server_id_list,
            folder_id_list,
          },
        })
      },
    }),

    // Get RS preview images as zip
    getRsPreviewZip: builder.mutation<string[], GetRsPreviewRequest>({
      query: ({ worklist_id }: GetRsPreviewRequest) => ({
        url: `/worklist/${worklist_id}/images/rs/zip`,
        method: 'GET',
        responseHandler: async (response: Response) => {
          if (response.status !== 200) {
            return response.json()
          }

          const arrayBuffer = await response.arrayBuffer()
          const zip = await JSZip.loadAsync(arrayBuffer)

          const imageBlobs = await Promise.all(
            Object.keys(zip.files)
              .filter((filename) => /\.(png)$/i.test(filename))
              .map(async (filename) => {
                const file = zip.files[filename]
                const blob = await file.async('blob')
                return URL.createObjectURL(blob)
              }),
          )

          return imageBlobs
        },
        cache: 'no-cache',
      }),
    }),
  }
}
