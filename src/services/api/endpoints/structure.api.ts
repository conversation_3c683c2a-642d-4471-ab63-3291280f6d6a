import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../helpers'
import type { BaseResponse } from '../types/common.type'

interface GetStructureRequest {
  name?: string
}

interface GetStructureResponse {
  structures: StructureType[]
}

interface GetStructureConfigResponse {
  structures: StructureConfigType[]
  category_type: string[]
  customized_structures_type: string[]
}

interface PutStructureRequest {
  structures: StructureType[]
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function createStructureEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get structures
    getStructure: builder.mutation<GetStructureResponse, GetStructureRequest>({
      query: (params: GetStructureRequest) => createApiQuery({
        url: '/structure',
        method: 'GET',
        params,
      }),
    }),

    // Update structures
    putStructure: builder.mutation<BaseResponse, PutStructureRequest>({
      query: ({ structures }: PutStructureRequest) => createApiQuery({
        url: '/structure',
        method: 'PUT',
        body: { structures },
      }),
    }),

    // Get structure configuration
    getStructureConfig: builder.mutation<GetStructureConfigResponse, void>({
      query: () => createApiQuery({
        url: '/structure',
        method: 'GET',
        params: { config_mode: true },
      }),
    }),
  }
}
