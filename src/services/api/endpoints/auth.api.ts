import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../helpers'
import type { LoginRequest, LoginResponse } from '../types/auth.type'
import type { BaseResponse } from '../types/common.type'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function createAuthEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // User login
    postLogin: builder.mutation<LoginResponse, LoginRequest>({
      query: (credentials: LoginRequest) => createApiQuery({
        url: '/login',
        method: 'POST',
        body: credentials,
      }),
    }),

    // User logout
    deleteLogin: builder.mutation<BaseResponse, void>({
      query: () => createApiQuery({
        url: '/login',
        method: 'DELETE',
      }),
    }),
  }
}
