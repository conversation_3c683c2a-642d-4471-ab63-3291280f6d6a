import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../helpers'
import type { BaseResponse } from '../types/common.type'

interface GetProtocolRequest {
  name?: string
}

interface GetProtocolResponse {
  protocols: ProtocolType[]
}

interface GetProtocolDetailRequest {
  id: number
}

interface GetProtocolDetailResponse extends ProtocolDetailType { }

interface CheckProtocolNameRequest {
  name: string
}

interface PostProtocolRequest extends ProtocolDetailType { }

interface PutProtocolRequest extends ProtocolDetailType {
  id: number
}

interface DeleteProtocolRequest {
  id: number
}

interface PutProtocolSortRequest {
  protocol_sort: number[]
}

interface PatchProtocolStatusRequest {
  id: number
  status: string
  name?: string
}

interface GetProtocolConfigResponse {
  protocols: ProtocolConfigType[]
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function createProtocolEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get protocols (query for caching)
    getProtocol: builder.query<GetProtocolResponse, GetProtocolRequest>({
      query: (params: GetProtocolRequest) => createApiQuery({
        url: '/protocol',
        method: 'GET',
        params,
      }),
    }),

    // Get protocol configuration
    getProtocolConfig: builder.mutation<GetProtocolConfigResponse, void>({
      query: () => createApiQuery({
        url: '/protocol',
        method: 'GET',
        params: { config_mode: true },
      }),
    }),

    // Get protocol detail
    getProtocolDetail: builder.mutation<GetProtocolDetailResponse, GetProtocolDetailRequest>({
      query: ({ id }: GetProtocolDetailRequest) => createApiQuery({
        url: `/protocol/${id}/detail`,
        method: 'GET',
      }),
    }),

    // Get worklist protocol detail (for update use protocol)
    getWorklistProtocolDetail: builder.mutation<GetProtocolDetailResponse, GetProtocolDetailRequest>({
      query: ({ id }: GetProtocolDetailRequest) => createApiQuery({
        url: `/protocol/${id}/detail`,
        method: 'GET',
      }),
    }),

    // Check protocol name availability
    checkProtocolName: builder.mutation<BaseResponse, CheckProtocolNameRequest>({
      query: ({ name }: CheckProtocolNameRequest) => createApiQuery({
        url: '/protocol/check',
        method: 'POST',
        body: { name },
      }),
    }),

    // Create new protocol
    postProtocol: builder.mutation<BaseResponse, PostProtocolRequest>({
      query: (data: PostProtocolRequest) => createApiQuery({
        url: '/protocol',
        method: 'POST',
        body: data,
      }),
    }),

    // Update protocol
    putProtocol: builder.mutation<BaseResponse, PutProtocolRequest>({
      query: ({ id, ...data }: PutProtocolRequest) => createApiQuery({
        url: `/protocol/${id}`,
        method: 'PUT',
        body: data,
      }),
    }),

    // Delete protocol
    deleteProtocol: builder.mutation<BaseResponse, DeleteProtocolRequest>({
      query: ({ id }: DeleteProtocolRequest) => createApiQuery({
        url: `/protocol/${id}`,
        method: 'DELETE',
      }),
    }),

    // Update protocol sort order
    putProtocolSort: builder.mutation<BaseResponse, PutProtocolSortRequest>({
      query: ({ protocol_sort }: PutProtocolSortRequest) => {
        return createApiQuery({
          url: '/protocol/sort',
          method: 'PUT',
          body: { protocol_sort },
        })
      },
    }),

    // Update protocol status
    patchProtocolStatus: builder.mutation<BaseResponse, PatchProtocolStatusRequest>({
      query: ({ id, status }: PatchProtocolStatusRequest) => {
        return createApiQuery({
          url: `/protocol/${id}/status`,
          method: 'PATCH',
          body: { status },
        })
      },
      // Optimistic update to immediately reflect status change in UI
      async onQueryStarted({ id, status }, { dispatch, getState }) {
        // Get current protocols from store
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const state = getState() as any
        const { protocols } = state.protocolReducer

        // Update the protocol status optimistically
        const updatedProtocols = protocols.map((item: ProtocolType) => {
          return item.id === id ? { ...item, status } : item
        })

        // Dispatch the update to Redux store
        dispatch({
          type: 'protocol/updateProtocols',
          payload: updatedProtocols,
        })
      },
    }),
  }
}
