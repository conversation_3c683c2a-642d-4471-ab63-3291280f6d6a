import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../helpers'
import type { BaseResponse } from '../types/common.type'
import type {
  GetNotifyRequest,
  GetNotifyResponse,
  DeleteNotifyRequest,
} from '../types/notify.type'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function createNotifyEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get notifications with token refresh
    getNotify: builder.mutation<GetNotifyResponse, GetNotifyRequest>({
      query: (params: GetNotifyRequest) => createApiQuery({
        url: '/notify',
        method: 'GET',
        params,
      }),
    }),

    // Get notifications without token refresh (for polling)
    reGetNotify: builder.mutation<GetNotifyResponse, GetNotifyRequest>({
      query: (params: GetNotifyRequest) => createApiQuery({
        url: '/notify',
        method: 'GET',
        params,
      }),
    }),

    // Delete notification
    deleteNotify: builder.mutation<BaseResponse, DeleteNotifyRequest>({
      query: (params: DeleteNotifyRequest) => createApiQuery({
        url: '/notify',
        method: 'DELETE',
        params,
      }),
    }),
  }
}
