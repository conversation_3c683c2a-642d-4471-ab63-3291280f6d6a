import type { EndpointBuilder } from '@reduxjs/toolkit/query/react'

import { createApiQuery } from '../helpers'
import type { BaseResponse } from '../types/common.type'

interface GetRemoteRequest {
  type?: string
}

interface GetRemoteResponse {
  remote_server_list: RemoteServerType[]
  folder_list: FolderType[]
}

interface GetRemoteConfigResponse {
  remote_server_list: RemoteConfigType[]
  folder_list: RemoteConfigType[]
}

interface PutRemoteRequest {
  type: string
  remote_server_list: RemoteServerType[]
  folder_list: FolderType[]
}

interface PostRemoteServerRequest {
  type: string
  name: string
  description: string
  ae_title: string
  ip: string
  port: string
}

interface DeleteRemoteServerRequest {
  type: string
  ae_title: string
  ip: string
  port: string
}

interface PostFolderRequest {
  type: string
  folder_name: string
  folder_description: string
  folder_path: string
  folder_account?: string
  folder_password?: string
}

interface DeleteFolderRequest {
  type: string
  folder_name: string
  folder_path: string
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function createRemoteEndpoints(builder: EndpointBuilder<any, any, any>) {
  return {
    // Get remote servers and folders
    getRemote: builder.mutation<GetRemoteResponse, GetRemoteRequest>({
      query: (params: GetRemoteRequest) => createApiQuery({
        url: '/source_destination',
        method: 'GET',
        params,
      }),
    }),

    // Get remote status (for polling without refresh token)
    getRemoteStatus: builder.mutation<GetRemoteResponse, GetRemoteRequest>({
      query: (params: GetRemoteRequest) => createApiQuery({
        url: '/source_destination',
        method: 'GET',
        params,
      }),
    }),

    // Get remote configuration
    getRemoteConfig: builder.mutation<GetRemoteConfigResponse, void>({
      query: () => createApiQuery({
        url: '/source_destination',
        method: 'GET',
        params: { config_mode: true },
      }),
    }),

    // Update remote configuration
    putRemote: builder.mutation<BaseResponse, PutRemoteRequest>({
      query: ({ type, remote_server_list, folder_list }: PutRemoteRequest) => createApiQuery({
        url: '/source_destination',
        method: 'PUT',
        params: { type },
        body: { remote_server_list, folder_list },
      }),
    }),

    // Add remote server
    postRemoteServer: builder.mutation<BaseResponse, PostRemoteServerRequest>({
      query: ({ type, ...data }: PostRemoteServerRequest) => {
        return createApiQuery({
          url: `/source_destination/${type}/remote_server`,
          method: 'POST',
          body: data,
        })
      },
    }),

    // Delete remote server
    deleteRemoteServer: builder.mutation<BaseResponse, DeleteRemoteServerRequest>({
      query: ({ type, ...data }: DeleteRemoteServerRequest) => createApiQuery({
        url: `/source_destination/${type}/remote_server`,
        method: 'DELETE',
        body: data,
      }),
    }),

    // Add folder
    postFolder: builder.mutation<BaseResponse, PostFolderRequest>({
      query: ({ type, ...data }: PostFolderRequest) => createApiQuery({
        url: `/source_destination/${type}/folder`,
        method: 'POST',
        body: data,
      }),
    }),

    // Delete folder
    deleteFolder: builder.mutation<BaseResponse, DeleteFolderRequest>({
      query: ({ type, ...data }: DeleteFolderRequest) => createApiQuery({
        url: `/source_destination/${type}/folder`,
        method: 'DELETE',
        body: data,
      }),
    }),
  }
}
