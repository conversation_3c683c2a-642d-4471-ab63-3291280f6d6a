import { PlusOutlined } from '@ant-design/icons'
import {
  Button, Checkbox, Flex, Input, Modal, Select, Table,
} from 'antd'
import type { ColumnsType } from 'antd/es/table/interface'
import trim from 'lodash-es/trim'

import { DeleteIcon } from 'src/assets/icons'
import ButtonGroup from 'src/components/Button/ButtonGroup'
import DebouncedWithHOC from 'src/components/DebouncedWithHOC'
import EmptyBox from 'src/components/EmptyBox'
import FieldExtra from 'src/components/Form/FieldExtra'
import ColorPicker from 'src/components/Popup/ColorPicker'
import { useAntModal } from 'src/hook/useAntModal'
import i18n from 'src/i18n'
import { useAppDispatch, useAppSelector } from 'src/store/hook'
import {
  addCustomizedStructures,
  deleteCustomizedStructures,
  updateCustomizedStructures,
  updateDetail,
} from 'src/store/reducers/detailSlice'
import { color } from 'src/styles/utils/variables'
import { verifyDetailState } from 'src/utils/verify'

import Operations, { getButtonGroup } from './Operations'

import 'src/styles/components/ListLayout.css'

const getErrorMessage = (errors: { unique: boolean, required: boolean, limitation: boolean }) => {
  if (errors.required) {
    return i18n.t('form_rules.enter_variable', {
      variable: i18n.t('titles.structure_id'),
      joinArrays: ' ',
    })
  }
  if (errors.unique) {
    return i18n.t('form_rules.variable_unique', {
      variable: i18n.t('titles.structure_id'),
      joinArrays: ' ',
    })
  }
  if (errors.limitation) {
    return i18n.t('form_rules.character_limitation', {
      count: 16,
      joinArrays: ' ',
    })
  }
  return ''
}

function ProcessTable() {
  // redux
  const dispatch = useAppDispatch()
  const { customizedStructureType, structureConfig, configRequired } = useAppSelector((state) => state.configReducer)
  const filedStructures = configRequired.customized_structures
  const { detail } = useAppSelector((state) => state.detailReducer)
  const dataSource = detail.customized_structures
  const validStructure = detail.structures.map((item) => item.id)
  const handleAdd = () => {
    dispatch(addCustomizedStructures())
  }
  const handleDelete = (data: CustomizedStructuresType) => {
    dispatch(deleteCustomizedStructures({ data }))
  }
  const handleChange = (data: CustomizedStructuresType) => {
    dispatch(updateCustomizedStructures({ data }))
  }

  // modal
  const clearAllModal = useAntModal({
    modalProps: {
      title: i18n.t('buttons.clear_all'),
      children: i18n.t('directions.confirm_clear_all'),
      width: 360,
      okText: i18n.t('buttons.yes_clear'),
      onOk() {
        dispatch(updateDetail({ customized_structures: [] }))
      },
      cancelText: i18n.t('buttons.no_cancel'),
      styles: { body: { padding: '60px 20px' } },
    },
  })

  const deleteItemModal = useAntModal<CustomizedStructuresType>({
    modalProps: {
      title: 'Delete Confirmation',
      children: 'Are you sure you want to delete this row?',
      okText: i18n.t('buttons.delete'),
      width: 400,
      centered: true,
      onOk(record) {
        handleDelete(record)
      },
      styles: {
        body: {
          padding: '56px 20px',
        },
      },
    },
  })

  const columns: ColumnsType<CustomizedStructuresType> = [
    {
      title: i18n.t('titles.show_rs'),
      key: 'show_rs',
      dataIndex: 'show',
      width: 50,
      render(value, record) {
        return (
          <div style={{ textAlign: 'center' }}>
            <Checkbox
              checked={value}
              onChange={() => handleChange({ ...record, show: !value })}
            />
          </div>
        )
      },
    },
    {
      title: i18n.t('titles.structure_id'),
      dataIndex: 'name',
      key: 'name',
      width: 200,
      onHeaderCell: () => ({ style: { padding: '0', paddingRight: '.5rem' } }),
      onCell: () => ({ style: { padding: '0', paddingRight: '.5rem' } }),
      render: (defaultValue, { id, name, ...record }) => {
        const text = trim((defaultValue as string))
        // Check for max length violation (>16 characters)
        const lengthError = text.length > 16
        const nameHasUnique = verifyDetailState.getInputDuplicateNameError(
          { id, name: text, ...record },
          {
            custom: dataSource,
            origin: structureConfig,
          },
        )
        const hasRequired = configRequired.customized_structures.find((item) => item.id === id && !defaultValue)
        const isError = nameHasUnique || hasRequired || lengthError
        const message = getErrorMessage({ unique: nameHasUnique, required: !!hasRequired, limitation: lengthError })

        return (
          <FieldExtra
            in={!!isError}
            timeout={100}
            message={message}
            status="error"
            styles={{
              message: {
                lineHeight: 1.25,
                zIndex: 20,
              },
            }}
          >
            <DebouncedWithHOC
              value={name}
              onChange={(value) => handleChange({ ...record, id, name: value })}
            >
              <Input
                required
                id={id ? `customized_name-${id}` : undefined}
                name="name"
                style={{ maxWidth: 200 }}
                status={isError ? 'error' : undefined}
                placeholder={
                  i18n.t('form_placeholders.enter_variable', {
                    variable: i18n.t('titles.structure_id'),
                    joinArrays: ' ',
                  })
                }
              />
            </DebouncedWithHOC>
          </FieldExtra>
        )
      },
    },
    {
      title: i18n.t('titles.volume_type'),
      dataIndex: 'type',
      key: 'type',
      width: 220,
      onHeaderCell: () => ({ style: { padding: '0 .5rem' } }),
      onCell: () => ({ style: { padding: '0 .5rem' } }),
      render: (type, record) => {
        const hasRequired = configRequired.customized_structures
          .find((item) => item.id === record.id && !type)

        return (
          <Select
            showSearch
            value={type}
            status={hasRequired ? 'error' : undefined}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            placeholder={i18n.t('form_placeholders.select_volume')}
            onChange={(value: string) => {
              handleChange({ ...record, type: value })
            }}
            options={customizedStructureType.map((item) => ({ label: item, value: item }))}
            className="add-empty-list-item-type"
            style={{ width: '100%' }}
          />
        )
      },
    },
    {
      title: i18n.t('titles.color_code'),
      dataIndex: 'color_code',
      key: 'color_code',
      width: 93,
      onHeaderCell: () => ({ style: { padding: '0 .5rem' } }),
      onCell: () => ({ style: { padding: '0 .5rem' } }),
      render(_, record) {
        return (
          <ColorPicker
            key={`color-picker-${record.id}`}
            value={record.color_code}
            onChange={(value) => {
              handleChange({ ...record, color_code: `#${value.toHex()}` })
            }}
          />
        )
      },
    },
    {
      title: i18n.t('titles.operations'),
      key: 'operations',
      dataIndex: 'mode',
      onCell: () => ({ style: { padding: 0 } }),
      render(mode, record) {
        const title = mode
          ? i18n.t(`tooltips.operations.${mode.toLowerCase()}`)
          : i18n.t('descriptions.select_operation_structure')
        const filed = filedStructures
          .find((item) => item.id === record.id && verifyDetailState.getOperationsErrors(record, validStructure))

        const style: React.CSSProperties = filed ? {
          border: '1px solid ',
          borderColor: color.error,
        } : {
          borderLeft: '1px solid',
          borderRight: '1px solid',
          borderColor: color.gray[1].variants,
        }

        return (
          <div style={{ background: color.gray[5], ...style }}>
            <Flex
              justify="space-between"
              align="center"
              component="header"
              style={{
                padding: ' 4px 8px 4px 16px',
                color: color.gray[1].default,
                background: color.gray[3],
                borderBottom: '1px solid',
                borderColor: color.gray[1].variants,
              }}
            >
              <h5 style={{ margin: 0, fontSize: 14, lineHeight: 1.75 }}>{title}</h5>
              {record.mode && (
                <ButtonGroup
                  group={getButtonGroup(record, handleChange)}
                  type="link"
                  size="small"
                />
              )}
            </Flex>
            <div style={{ padding: 16, background: color.gray[5] }}>
              <Operations
                data={record}
                onChange={handleChange}
                validStructure={validStructure}
                options={structureConfig
                  .filter((item) => validStructure.includes(item.id))
                  .map((item) => ({
                    label: item.customized_name || item.efai_structure_name,
                    value: item.id,
                  }))}
              />
            </div>
          </div>
        )
      },
    },
    {
      key: 'delete',
      width: 50,
      render(_, record) {
        return (
          <Button
            icon={<DeleteIcon />}
            size="small"
            onClick={() => {
              deleteItemModal.trigger(record)
            }}
          />
        )
      },
    },
  ]

  return (
    <section style={{ minWidth: 1000 }}>
      <hgroup style={{
        position: 'sticky',
        zIndex: 50,
        top: -32,
        paddingBottom: '1rem',
        background: color.gray[4],
      }}
      >
        <h4 style={{
          padding: '4px 8px',
          borderRadius: 4,
          fontWeight: 700,
          color: color.gray[1].default,
          background: color.gray[3],
        }}
        >
          {i18n.t('titles.process_structures_list')}
        </h4>
        <Flex
          component="nav"
          justify="space-between"
          style={{ margin: '1rem 0' }}
        >
          <p style={{ fontSize: 14, color: color.gray[1].default }}>
            {i18n.t('descriptions.process_structures_list')}
          </p>
          <ButtonGroup group={[
            {
              key: 'clear',
              children: i18n.t('buttons.clear_all'),
              variant: 'outlined',
              onClick: clearAllModal.trigger,
              style: {
                background: color.gray[4],
                color: color.gray[1].default,
                borderColor: color.gray[1].default,
                fontWeight: 700,
              },
            },
            {
              key: 'save',
              icon: <PlusOutlined />,
              children: `${i18n.t('buttons.add')} ${i18n.t('titles.structures')}`,
              type: 'primary',
              onClick: handleAdd,
              style: {
                color: color.gray[4],
                fontWeight: 700,
              },
            },
          ]}
          />
        </Flex>
      </hgroup>
      <Table
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        scroll={{ x: 'max-content' }}
        locale={{
          emptyText: <EmptyBox description={i18n.t('descriptions.process_table_empty')} />,
        }}
      />
      <Modal {...clearAllModal.modalProps} />
      <Modal {...deleteItemModal.modalProps} />
    </section>
  )
}

export default ProcessTable
