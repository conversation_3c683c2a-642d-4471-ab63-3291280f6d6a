/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react'

import {
  Descriptions, Tabs, type TabsProps, type DescriptionsProps,
  Input,
  Select,
  Switch,
  Flex,
  Alert,
  ConfigProvider,
  Tooltip,
} from 'antd'
import { Content, Footer } from 'antd/es/layout/layout'
import { CSSTransition } from 'react-transition-group'

import { AlertInfoCircle, EditIcon } from 'src/assets/icons'
import { ConfigLayoutHead, type ConfigLayoutHeadProps } from 'src/components/Head'
import Loading from 'src/components/Loading'
import LeaveConfirmModal from 'src/components/Modal/LeaveConfirmModal'
import type { ConfigDescriptionItem } from 'src/context/moduleList'
import i18n from 'src/i18n'
import { useAppDispatch } from 'src/store/hook'
import { resetConfigState } from 'src/store/reducers/configSlice'
import { resetDetailState } from 'src/store/reducers/detailSlice'
import { color } from 'src/styles/utils/variables'

type DescriptionDataType = DescriptionsProps['items']
type Props = {
  header: ConfigLayoutHeadProps
  tabs: TabsProps['items']
  descriptionData: ConfigDescriptionItem[]
  footer?: React.ReactNode
  loading?: boolean
  leaveBlocker?: boolean
  tabsProps?: Omit<TabsProps, 'items'>
  descriptionsProps?: DescriptionsProps
  showAlert?: boolean
}

const defaultInputStyle = {
  width: '100%',
  maxWidth: 250,
}

function NewConfigBasicLayout({
  header, tabs, descriptionData, footer, loading, leaveBlocker = false,
  tabsProps, descriptionsProps, showAlert = false,
}: Props) {
  // redux
  const dispatch = useAppDispatch()
  // 控制 Alert 顯示狀態
  const [alertVisible, setAlertVisible] = useState(showAlert)
  const [showIcon, setShowIcon] = useState(false)

  useEffect(() => {
    if (showAlert) {
      setAlertVisible(true) // 顯示 Alert
      setShowIcon(false)
      const timerAlert = setTimeout(() => {
        setAlertVisible(false)
      }, 10000)
      const timerIcon = setTimeout(() => setShowIcon(true), 10600)
      return () => {
        clearTimeout(timerAlert)
        clearTimeout(timerIcon)
      }
    }
    return () => { }
  }, [showAlert])

  const descriptionItems: DescriptionDataType = descriptionData
    .map(({
      label, value, type, props, required, tooltip,
    }) => {
      let children: React.ReactNode

      switch (type) {
        case 'input':
          children = (
            <Tooltip title={tooltip}>
              <span>
                <Input
                  size="small"
                  {...props}
                  suffix={<EditIcon style={{ fontSize: '1.5rem' }} />}
                  style={{ ...defaultInputStyle, ...props?.style }}
                />
              </span>
            </Tooltip>
          )
          break
        case 'select':
          children = (
            <Tooltip title={tooltip}>
              <Select style={{ ...defaultInputStyle, ...props?.style }} {...props} />
            </Tooltip>
          )
          break
        case 'switch':
          children = (
            <Tooltip title={tooltip}>
              <Flex align="center" justify="space-between" flex="0 0 96px">
                <span>
                  {value && i18n.t(`switch_options.${String(value).toLocaleLowerCase()}`)}
                </span>
                <Switch {...props} />
              </Flex>
            </Tooltip>
          )
          break
        default:
          children = value
          break
      }
      return {
        key: label,
        label: (
          <Tooltip title={tooltip}>
            <p style={{ width: 160 }}>
              {required && <span style={{ color: 'red' }}>*</span>} {i18n.t(`titles.${label}`)}:
            </p>
          </Tooltip>
        ),
        children,
      }
    })

  useEffect(() => {
    return () => {
      dispatch(resetDetailState())
      dispatch(resetConfigState())
    }
  }, [])

  return (
    <ConfigProvider theme={{
      components: {
        Alert: {
          colorText: color.primary.default,
          colorInfoBg: color.gray[2],
          colorInfoBorder: color.gray[2],
          defaultPadding: '0.25rem 0.5rem',
        },
      },
    }}
    >
      <ConfigLayoutHead {...header} />
      <Content style={{ height: '100%', overflow: 'auto' }}>
        <Descriptions
          column={{ md: 2, lg: 2, xl: 3 }}
          items={descriptionItems}
          colon={false}
          {...descriptionsProps}
        />
        <section style={{ position: 'relative', marginTop: '2rem' }}>
          {/* 使用 React Transition Group 控制淡出 */}
          <CSSTransition
            in={alertVisible}
            timeout={500} // 0.5 秒淡出
            classNames="fade"
            unmountOnExit
          >
            <Alert
              icon={<AlertInfoCircle style={{ fontSize: '1.5rem' }} />}
              message={i18n.t('paragraphs.config_changed')}
              className="setting-change-alert"
              type="info"
              showIcon
              style={{
                zIndex: 9,
              }}
            />
          </CSSTransition>
          {showIcon && (
            <AlertInfoCircle
              className="setting-change-alert"
              style={{
                fontSize: '1.5rem',
                top: 13,
                right: 17,
                zIndex: 10,
              }}
            />
          )}
          <Tabs
            items={tabs}
            {...tabsProps}
            className="setting-change-info"
          />
        </section>
      </Content>
      <Footer
        style={{
          display: footer ? 'flex' : 'none',
          gap: 16,
          justifyContent: 'flex-end',
          lineHeight: '24px',
        }}
      >{footer}
      </Footer>
      <Loading open={loading} />
      <LeaveConfirmModal condition={leaveBlocker} />
    </ConfigProvider>
  )
}

export default NewConfigBasicLayout
export type { DescriptionDataType }
