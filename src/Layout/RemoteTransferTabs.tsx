import { TabsProps } from 'antd'

import SendToDesination from 'src/components/Form/SendToDesination'
import ConfigSubTabs from 'src/components/Tabs/ConfigSubTabs'
import TransferTabs from 'src/components/Transfer/TransferTabs'
import i18n from 'src/i18n'
import { useAppSelector } from 'src/store/hook'
import { color } from 'src/styles/utils/variables'

type Props = {
  detail: ProtocolDetailType
  onUpdateRemote: (
    type: SourceDestinationKey,
    remote: RemoteCategoryKey,
    value: RemoteType[]
  ) => void
}

function RemoteTransferTabs({ detail, onUpdateRemote }: Props) {
  // redux
  const { configRequired } = useAppSelector((state) => state.configReducer)

  // handlers
  const handleRemoveDestination = () => {
    onUpdateRemote('destination', 'remote_server', [])
    onUpdateRemote('destination', 'folder', [])
  }

  const tabItems: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <span
          style={{ color: color.gray[0].default }}
          className={(configRequired.source) ? 'red-ball right-move' : ''}
        >
          {i18n.t('titles.source')}
        </span>
      ),
      children: (
        <TransferTabs
          type="source"
          checkList={detail}
          onUpdateRemote={onUpdateRemote}
        />),
    },
    {
      key: '2',
      label: (
        <span
          style={{ color: color.gray[0].default }}
          className={(configRequired.destination) ? 'red-ball right-move' : ''}
        >{i18n.t('titles.destination')}
        </span>),
      children: (
        <SendToDesination
          defaultOpen={Object.values(detail.destination).some((list) => list.length > 0)}
          onChange={(checked) => !checked && handleRemoveDestination()}
        >
          <TransferTabs
            type="destination"
            checkList={detail}
            onUpdateRemote={onUpdateRemote}
          />
        </SendToDesination>
      ),
    },
  ]
  return (
    <ConfigSubTabs defaultActiveKey="1" items={tabItems} />
  )
}

export default RemoteTransferTabs
