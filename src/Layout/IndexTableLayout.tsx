import type { ReactNode } from 'react'

import { Col, Row } from 'antd'
import type { FormInstance } from 'antd/es/form'
import { Content } from 'antd/es/layout/layout'
import type { SorterResult } from 'antd/es/table/interface'

import { Head } from 'src/components/Head'
import { SearchList } from 'src/components/List'
import WorklistGroupTable from 'src/components/Table/WorklistGroupTable'
import i18n from 'src/i18n'

interface IndexTableLayoutProps {
  // Page state
  pageTitle: string
  searchOptions: OptionType[]
  isHistoryPage: boolean

  // Form
  form: FormInstance

  // Loading states
  groupTableLoading: boolean

  // Sorting states
  groupSorted: SorterResult<WorklistGroupType>

  // Handlers
  handleSearch: (page?: number, sorter?: any) => void

  // Table component to render
  children: ReactNode
}

/**
 * Shared UI Layout component for table-based pages (Task/History)
 * Accepts table component as prop for flexibility
 */
function IndexTableLayout({
  pageTitle,
  searchOptions,
  isHistoryPage,
  form,
  groupTableLoading,
  groupSorted,
  handleSearch,
  children,
}: IndexTableLayoutProps) {
  return (
    <>
      <Head>{i18n.t(pageTitle)}</Head>
      <Content
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          padding: '1rem',
          gap: '1rem',
        }}
      >
        <Row>
          <SearchList
            form={form}
            optionList={searchOptions}
            handleSearch={handleSearch}
            datePicker={isHistoryPage}
          />
        </Row>
        <Row style={{ height: '100%', overflow: 'auto' }}>
          <Col span={8}>
            <WorklistGroupTable
              tableLoading={groupTableLoading}
              history={isHistoryPage}
              sortedInfo={groupSorted}
              handleDataChange={handleSearch}
            />
          </Col>
          <Col span={16} style={{ height: '100%' }}>
            {children}
          </Col>
        </Row>
      </Content>
    </>
  )
}

export default IndexTableLayout
