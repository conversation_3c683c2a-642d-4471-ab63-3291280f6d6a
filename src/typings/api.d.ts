type Res = {
  result: string
}

type LoginReq = {
  account: string
  password: string
}

type LoginRes = {
  user_name: string
  token: string
}

type LogoutReq = {
  token: string
}

type GetNotifyReq = {
  quantity: number
}

type GetNotifyRes = {
  total_notify: number
  notify_list: NotifyType[]
}

type DeleteNotifyReq = {
  notify_id?: number
}

type GetWorklistGroupReq = {
  page?: number
  patient_id?: string
  study_status?: string
  study_date_range_start?: string
  study_date_range_end?: string
  order_key?: string
  ascend?: boolean
  history?: boolean
}

type GetWorklistGroupRes = {
  current_page: number
  page_size: number
  total_data: number
  worklist_group: WorklistGroupType[]
}

type PutPrioritizeWorklistGroupReq = {
  worklist_group_id: number
}

type PutWorklistGroupStatusReq = {
  worklist_group_id: number
  status: string
}

type GetWorklistReq = {
  worklist_group_id: number
  order_key?: string
  ascend?: boolean
}

type GetWorklistRes = WorklistType & {}

type PatchWorklistStatusReq = {
  id: number
  status: string
}

type GetWorklistDetailReq = {
  id: number
}
type GetWorklistDetailRes = WorklistDetailType & {}

type DetailTypeWithAPI =
  (Omit<CustomizedWorklistDetailType, 'structures'> & { structures: StructureSortType[] }) |
  WorklistProtocolType

type PutWorklistReq = {
  id: number
  data: DetailTypeWithAPI
}

type PostWorklistRedrawReq = {
  worklist_id: number
  data: DetailTypeWithAPI
}

type PostRsResendReq = {
  id: number
  remote_server_id_list: RemoteType[]
  folder_id_list: RemoteType[]
}

type GetStructureReq = {
  name?: string
}

type GetRsPrevew = {
  worklist_id: number | string
}

type GetStructureRes = {
  structures: StructureType[]
}

type GetStructureConfigRes = {
  structures: StructureConfigType[]
  category_type: string[]
  customized_structures_type: string[]
}

type PutStructureReq = {
  structures: StructureType[]
}

type GetProtocolReq = {
  name?: string
}

type GetProtocolRes = {
  protocols: ProtocolType[]
}

type GetProtocolConfigRes = {
  protocols: ProtocolConfigType[]
}

type CheckProtocolNameReq = {
  name: string
}

type PostProtocolReq = ProtocolDetailType & {}

type PutProtocolReq = ProtocolDetailType & {
  id: number
}

type DeleteProtocolReq = {
  id: number
}

type GetProtocolDetailReq = {
  id: number
}

type GetProtocolDetailRes = ProtocolDetailType & {}

type PutProtocolSortReq = {
  protocol_sort: number[]
}

type PatchProtocolStatusReq = {
  id: number
  status: string
  name?: string
}

type GetRemoteReq = {
  type?: string
}

type GetRemoteRes = {
  remote_server_list: RemoteServerType[]
  folder_list: FolderType[]
}

type GetRemoteConfigRes = {
  remote_server_list: RemoteConfigType[]
  folder_list: RemoteConfigType[]
}

type PutRemoteReq = {
  type: string
  remote_server_list: RemoteServerType[]
  folder_list: FolderType[]
}

type PostRemoteServerReq = {
  type: string
  name: string
  description: string
  ae_title: string
  ip: string
  port: string
}

type DeleteRemoteServerReq = {
  type: string
  ae_title: string
  ip: string
  port: string
}

type PostFolderReq = {
  type: string
  folder_name: string
  folder_description: string
  folder_path: string
  folder_account?: string
  folder_password?: string
}

type DeleteFolderReq = {
  type: string
  folder_name: string
  folder_path: string
}

type GetDicomReq = {
  id: number
  rs?: boolean
  ct?: boolean
}
