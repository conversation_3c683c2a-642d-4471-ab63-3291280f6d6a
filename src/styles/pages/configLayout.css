.ant-layout-content:has(.config-layout) {
  min-height: 600px;
  overflow-x: auto;
}
.ant-layout-content > section.config-layout > .ant-tabs.ant-tabs-top {
  height: calc(100% - 120px);
}

.config-layout {
  height: calc(100% - 64px);
  display: flex;
  flex-direction: column;
  overflow: auto;
  :is(.ant-list-items, .add-list-form) {
    overflow: inherit;
  }
  & > :is(.ant-tabs, .ant-form) {
    min-width: 756px;
  }
}

.config-layout .add-list-form {
  min-height: 96px;
}

.config-layout .ant-list {
  height: calc(100% - 90px);
}

.edit-form .ant-form-item.edit-icon .ant-form-item-control-input {
  position: relative;
  &:hover:before {
    content: url("../../assets/icons/edit.svg");
    position: absolute;
    right: 0;
    top: 1px;
    color: #fff;
  }
}

.ant-form-item .ant-form-item-row:has(.ant-form-item-label label) {
  .ant-form-item-control-input-content {
    display: flex;
    gap: 8px;
    align-items: center;
    color: var(--color-gray_0);
  }
}

.config-sub-tabs.ant-tabs.ant-tabs-top.ant-tabs-card > .ant-tabs-nav {
  margin-bottom: 1.5rem;
  &::before {
    border: none;
  }
  .ant-tabs-tab {
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    overflow: hidden;
  }
  .ant-tabs-tab.ant-tabs-tab-active {
    background: var(--color-primary);
    .ant-tabs-tab-btn > span {
      color: var(--color-gray_0);
    }
  }
}

.edit-structure {
  background: var(--color-gray_3);
  border-radius: 5px;
  height: calc(100% - 60px);
  container-type: size;
  display: flex;
  flex-direction: column;
}

.edit-structure-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  margin: 0;
  border-bottom: 2px solid var(--color-gray_4);
  background: var(--color-gray_3);
}

.no-checkbox-border.ant-checkbox-wrapper {
  background: var(--color-gray_5);
  box-shadow: 0px 2px 0px 0px rgba(26, 38, 47, 0.4);
  border-radius: 4px;
  transition: background 0.3s;

  .ant-checkbox.ant-wave-target {
    display: none;
  }
  &:has(.ant-checkbox-checked) {
    background: var(--color-gray_2);
  }

  & span:last-child {
    height: 100%;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding-inline-start: 0.25rem;
    padding-inline-end: 0.75rem;
    line-height: 1.5;
  }
}

.edit-structure-filter-menu {
  & li:has(.ant-input) {
    position: relative;

    &::before {
      position: absolute;
      top: 3px;
      left: 10px;
      z-index: 5;
      width: 1rem;
      height: 1rem;
    }

    .ant-input {
      padding: 0 0.75rem 0 2rem;
      height: 100%;
      vertical-align: middle;
    }
  }
}

.edit-structure-main {
  padding: 0.5rem 1.25rem;
  background: var(--color-gray_3);
  overflow-y: auto;
  flex: 1;
}

.structure-tag-icon {
  width: 16px;
  height: 16px;
  transition: all 0.2s;
}

.structure-tag-icon.tag-checkable {
  background-image: url("../../assets/icons/ok-circle-2.svg");
}

.structure-tag:hover .structure-tag-icon {
  background-image: url("../../assets/icons/ok-circle-2.svg");
}

.structure-tag:hover .structure-tag-icon.tag-checkable {
  background-image: url("../../assets/icons/x-circle.svg");
}

.structure-tag.tag-checkable:hover .structure-tag-icon.tag-checkable {
  background-image: url("../../assets/icons/ok-circle-2.svg");
}

.ant-select.ant-tree-select.filter-select {
  min-width: 108px;
  width: auto;

  .ant-select-selector {
    padding: 0 1.75rem 0 0.25rem;
    box-shadow: 0px 2px 0px 0px rgba(26, 38, 47, 0.4);

    &::before {
      content: url("../../assets/icons/filter.svg");
      width: 28px;
      height: 28px;
      margin-right: 0.25rem;
    }

    .ant-select-selection-overflow {
      flex-wrap: nowrap;
      gap: 0.25rem;
    }
  }

  &.ant-select-focused .ant-select-selector {
    border: solid 1px var(--color-primary);
    box-shadow: none;
  }

  .ant-select-selection-item {
    height: 20px;
    margin: 0rem 0;
    border-radius: 1.25rem;
    background-color: rgba(255, 255, 255, 0.25);
    padding: 0 0.25rem 0 0.5rem;
    .ant-select-selection-item-content {
      font-size: 14px;
      line-height: 20px;
    }
  }
}

.ant-tree-select-dropdown.filter-select-dropdown {
  padding: 0.25rem 0;
}

.ant-tree-select-dropdown.filter-select-dropdown .ant-select-tree {
  .ant-select-tree-treenode {
    padding: 0.25rem 0.75rem;
    &:first-child {
      padding-bottom: 8px;
      border-bottom: 2px solid var(--color-gray_3);
    }
  }
  .ant-select-tree-indent,
  .ant-select-tree-switcher.ant-select-tree-switcher-noop,
  .ant-select-tree-switcher.ant-select-tree-switcher_open {
    display: none;
  }
}

.filter-select-dropdown.ant-select-dropdown.ant-cascader-dropdown .ant-cascader-menu {
  height: auto;
}

.check-count-ball {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  font-size: 12px;
  border-radius: 50%;
  background: var(--color-gray_0_alpha);
  color: var(--color-gray_0);
}
