.protocol-table {
  width: 100%;

  :is(tbody tr, thead) {
    border-bottom: var(--color-gray_2) solid 1px;
    transition: all 0.12s;
    & td textarea {
      padding: 0;
    }
  }
}
.switch-box {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  & p {
    margin: 0;
  }
}

.ant-modal-body:has(.radio-group) {
  .ant-form.ant-form-vertical.ant-form-hide-required-mark {
    width: 100%;
    height: 100%;
  }
  .ant-form-item-control-input-content {
    height: 100%;
  }

  .ant-form-item-control-input {
    height: calc(100% - 112px);
  }

  .radio-group {
    height: 100%;
    display: flex;
    flex-direction: column;

    .ant-row.ant-form-item-row {
      height: 100%;
      display: flex;
      flex-direction: column;
      .ant-colant-form-item-control {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
    }

    :is(p) {
      font-size: 1rem;
      font-weight: 300;
      line-height: 2.25;
    }
    :is(h3) {
      margin: 0;
    }
    .ant-table.ant-table-fixed-header,
    .ant-table-container {
      height: 100%;
    }

    .ant-table-header {
      border-top: 1px solid var(--color-gray_1);
      border-bottom: 1px solid var(--color-gray_1);
    }

    .ant-table-row {
      position: relative;
      transition: all 0.2s;
      &:hover {
        color: var(--color-primary_variants);
        .ant-table-cell:first-child {
          background-image: url("/src/assets/icons/ok-circle.svg");
        }
      }
    }

    & td.ant-table-cell {
      padding: 1rem 0.5rem;
      &.ant-table-cell-row-hover {
        background: transparent;
      }
      &:first-child {
        background: center/50% no-repeat;
        height: 100%;
        width: 100%;
      }
    }

    .select-table-row.ant-table-row {
      color: var(--color-primary);
      .ant-table-cell:first-child {
        background-image: url("/src/assets/icons/ok-circle-blue.svg");
      }
      &:hover .ant-table-cell:first-child {
        background-image: url("/src/assets/icons/ok-circle-blue.svg");
      }
    }
  }
}

.radio-button {
  position: relative;
  display: inline-block;
  padding: 0.5rem 1.25rem;
  line-height: 2rem;
  border-radius: 0.25rem;
  background: var(--color-gray_2);
  color: var(--color-primary);
  box-shadow: 0px 2px 0px 0px rgba(26, 38, 47, 0.4);
  transition: all 0.2s;

  :is(input) {
    display: none;
  }

  &:hover,
  &:has(input[type="radio"]:checked) {
    background: var(--color-gray_3);
  }

  &:hover {
    color: var(--color-primary_variants);
    &::before {
      content: url("../../assets/icons/ok-circle.svg");
      margin-right: 0.5rem;
    }
  }

  &:has(input[type="radio"]:checked)::before {
    content: url("../../assets/icons/ok-circle-blue.svg");
    margin-right: 0.5rem;
    box-shadow: none;
  }
}

.radio-group-list-item {
  display: table-row;
  position: relative;
  width: 100%;
  & td {
    text-align: left;
  }
  & input {
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
  }
  &:has(input[type="radio"]:checked) {
    color: var(--color-primary);
    /* position: absolute;
    content: url("../../assets/icons/ok-circle.svg"); */
  }
}

.add-empty-list-item-type.ant-select.ant-tree-select {
  :is(.ant-select-selector) {
    border-color: transparent;
    background-color: transparent;
  }
}
