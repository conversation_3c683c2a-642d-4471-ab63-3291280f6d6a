.ant-modal-content {
  top: 50% !important;
  margin: auto;
  padding: 0 !important;
  overflow: hidden;
}
.ant-modal-content .ant-modal-header {
  border-bottom: 1px solid var(--color-gray_4);
}

.ant-modal-title {
  text-align: center;
}

.ant-modal-content .ant-modal-body {
  padding: 2rem 0;
  text-align: center;
}
.ant-modal-content .ant-modal-body p {
  margin: 0;
}
.ant-modal-content .ant-modal-footer {
  text-align: center;
  padding: 0 0 1.5rem;
  display: flex;
  justify-content: center;
  gap: 16px;
}

.confirm-modal {
  max-width: 350px;
  min-width: 350px;
}
.confirm-modal .ant-modal-body {
  padding: 4rem 1.25rem;
  font-size: 18px;
  line-height: 2;
}

.create-new-modal {
  min-width: 750px;
  @media (max-width: 750px) {
    min-width: 95vw;
  }
  :is(.ant-modal-content, .ant-modal-header) {
    border: none;
    background: var(--color-gray_3);
  }

  .ant-modal-title {
    text-align: left;
  }
}

#create-protocol-form.ant-form {
  .ant-form-item .ant-form-item-explain-error {
    text-align: center;
  }

  .ant-form-item .ant-form-item-label {
    display: flex;
    justify-content: center;
    margin-bottom: 0.5rem;
    & > label {
      color: var(--color-gray_0);
      font-size: 1rem;
    }
  }
}

.confirm-create-new-modal {
  max-width: 75vw;
  min-width: 75vw;
}
.confirm-create-new-modal .ant-modal-header {
  border-bottom: none;
  margin: 0;
  .ant-modal-title {
    font-size: 1.5rem;
    text-align: start;
    padding: 1.5rem 1.75rem 1rem;
  }
}
.confirm-create-new-modal .ant-modal-footer {
  margin-top: 1.5rem;
}

.confirm-create-new-modal .ant-modal-body {
  max-height: 75vh;
  min-height: 75vh;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.confirm-create-new-modal .confirm-header {
  height: 150px;
  padding: 0 1.75rem;
  border-bottom: 1px solid var(--color-gray_1);
}

.confirm-create-new-modal .confirm-body {
  display: flex;
  position: relative;
  flex: 1;
  overflow: auto;
}

.confirm-create-new-modal .confirm-body-menu {
  position: sticky;
  top: 0;
  bottom: 0;
  margin: 0;
  flex: 1;
  padding: 2rem 1.75rem;
}

.confirm-create-new-modal .confirm-body-content {
  flex: 4;
  padding: 2rem 1.75rem;
  padding-left: 0;
}
.confirm-create-new-modal .confirm-body-content-section {
  margin-bottom: 3rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid var(--color-gray_1);
  /* height: 700px; */
}

.confirm-create-new-modal .section-title {
  font-size: 1.5rem;
  text-align: start;
  margin-bottom: 1.5rem;
  & ~ h4 {
    margin-top: 1.5rem;
    text-align: start;
    font-size: 1.25rem;
  }
}
.confirm-create-new-modal .section-main .ant-col {
  text-align: start;
}

.remote-tag-list {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-top: 1.25rem;
  flex-wrap: wrap;
}
.remote-tag-list p {
  flex: 0 1 7.25rem;
  text-align: start;
  font-size: 1rem;
}
.remote-tag-list span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0 0.75rem;
  background-color: var(--color-gray_4);
  border-radius: 0.25rem;
  cursor: pointer;
}
.remote-tag-list span::after {
  content: url(../../assets/icons/ok-circle-2.svg);
  position: relative;
  top: 1px;
}

.studies-list {
  padding: 0;
  list-style-type: none;
  margin: 0;
}

.studies-list-item {
  display: flex;
  gap: 1.5rem;
  padding: 0.75rem 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.16s;
}
.studies-list-item:hover {
  background-color: var(--color-gray_3);
}
.studies-list-item p {
  flex: 1 0 0;
  text-align: start;
}
.studies-list-item p:first-child {
  flex: 0 1 80px;
  text-align: left;
}
.studies-list-item .color-box {
  flex: 0 1 4.5rem;
  & span {
    display: block;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 0.25rem;
  }
}

.resend-modal h3,
.resend-modal p {
  text-align: left;
  /* margin: 0; */
}
.resend-modal hgroup {
  margin-bottom: 1.75rem;
}
.resend-modal .ant-tabs.ant-tabs-top.ant-tabs-card {
  padding-bottom: 0.5rem;
}
.resend-modal .ant-tabs-content-holder .ant-transfer-list {
  height: 45vh;
  max-width: 50%;
}

.useAlert .ant-modal-content {
  background: var(--color-gray_3);
  :is(.ant-modal-body) {
    padding: 1.5rem;
  }

  :is(.ant-modal-confirm-body) {
    position: relative;
    & svg {
      position: absolute;
      left: 0;
      top: 0;
    }
  }
  :is(.ant-modal-confirm-title, .ant-modal-confirm-content) {
    color: var(--color-gray_0);
    text-align: left;
    padding-left: 2.75rem;
  }
  :is(.ant-modal-confirm-title) {
    font-size: 1.25rem;
  }

  :is(.ant-modal-confirm-btns) {
    text-align: center;
    margin-top: 2rem;
  }

  :is(.ant-btn) {
    width: 6.25rem;
    background: transparent;
    border-radius: 5px;
    border: 1px solid var(--color-primary);
    color: var(--color-primary);

    &:hover,
    &:active {
      color: var(--color-gray_0);
      background: var(--color-primary);
    }
  }
}

.useAlert.useAlert-modal .ant-modal-body {
  height: 300px;
  display: flex;
  flex-direction: column;
}
.useAlert.useAlert-modal .ant-modal-confirm-body-wrapper {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
}

.useAlert.useAlert-modal :is(.ant-modal-confirm-body, .ant-modal-confirm-paragraph) {
  flex: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.useAlert.useAlert-modal .ant-modal-confirm-title,
.useAlert.useAlert-modal .ant-modal-confirm-content {
  padding-left: 0;
  flex: 0 1 auto;
}

.useAlert.useAlert-modal .ant-modal-confirm-body .ant-modal-confirm-title + .ant-modal-confirm-content {
  flex-basis: auto;
  font-size: 1rem;
}

.useAlert.useAlert-modal .ant-modal-confirm-body-wrapper .ant-modal-confirm-btns {
  margin-top: auto;
}

.about-modal-content .ant-modal-body {
  padding: 1rem 2rem 1.5rem;
  text-align: left;
}

.about-modal-content {
  :is(h3, h4) {
    margin-bottom: 0;
  }
  :is(h3) {
    font-size: 1.5rem;
    color: var(--color-gray_0);
  }
  :is(h4) {
    color: var(--color-gray_1);
    margin-bottom: 1rem;
  }
}

.description-list-item * {
  font-size: 14px;
}

.add-list-modal-body.ant-modal-body
  :is(.ant-input-outlined.ant-input-disabled, .ant-input-outlined.ant-input-disabled input[disabled])::placeholder {
  opacity: 0.5;
}
