import { useLocation } from 'react-router'

import useAlert from 'src/hook/useAlert'
import {
  useGetWorklistGroupMutation,
  useReGetWorklistGroupMutation,
  useReGetWorklistMutation,
} from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'

/**
 * Custom hook for managing worklist data and API operations
 * Shared between task-page and history-page
 */
export function useWorklistData() {
  const { pathname } = useLocation()
  const dispatch = useAppDispatch()
  const handleAlert = useAlert()

  const {
    worklistGroupFocus,
    worklistGroupPage,
    worklistGroupSearch,
  } = useAppSelector((state) => state.worklistReducer)

  const { message } = useAppSelector((state) => state.websocketReducer)

  const [getWorklistGroupMutation] = useGetWorklistGroupMutation()
  const [reGetWorklistGroupMutation] = useReGetWorklistGroupMutation()
  const [reGetWorklistMutation] = useReGetWorklistMutation()

  return {
    pathname,
    dispatch,
    handleAlert,
    worklistGroupFocus,
    worklistGroupPage,
    worklistGroupSearch,
    message,
    getWorklistGroupMutation,
    reGetWorklistGroupMutation,
    reGetWorklistMutation,
  }
}
