import React from 'react'

import type { MenuProps } from 'antd'
import { Layout, Dropdown } from 'antd'
import { useNavigate } from 'react-router'

import { UserIcon } from 'src/assets/icons'
import useAlert from 'src/hook/useAlert'
import i18n from 'src/i18n'
import { useDeleteLoginMutation } from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook/index'
import { kickOff } from 'src/store/reducers/authSlice'

import NotifyListDrawer from './NotifyListDrawer'
import Button from '../Button'

const { Header } = Layout

interface Props {
  children: string | React.ReactNode
  classNames?: {
    readonly header?: string
  }
}

const Head = React.forwardRef<HTMLElement, Props>((
  { children, classNames },
  ref,
) => {
  const { username } = useAppSelector((state) => state.authReducer)
  const [deleteLoginMutation] = useDeleteLoginMutation()
  const dispatch = useAppDispatch()
  const navigate = useNavigate()

  // hook
  const handleAlert = useAlert()

  const handleLogout = async () => {
    try {
      await deleteLoginMutation().unwrap()
    } catch (e) {
      dispatch(kickOff())
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    } finally {
      navigate('/')
    }
  }

  const userItems: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <Button
          type="button"
          onClick={handleLogout}
          style={{ background: 'none' }}
        >
          {i18n.t('buttons.logout')}
        </Button>
      ),
    },
  ]

  return (
    <Header className={`head ${classNames?.header}`} style={{ padding: 0 }} ref={ref}>
      <nav style={{ position: 'relative', zIndex: 200 }}>
        <h1>{children}</h1>
        <ul className="head-navbar">
          <li style={{ padding: 0 }}>
            <Dropdown menu={{ items: userItems }}>
              <button type="button" onClick={(e) => e.preventDefault()}>
                {username}
                <UserIcon />
              </button>
            </Dropdown>
          </li>
          <li>
            <NotifyListDrawer />
          </li>
        </ul>
      </nav>
    </Header>
  )
})

Head.displayName = 'Head'
export default Head
