function createHistoryItems(
  length: number,
): (isItem: boolean) => Array<HistoryGroupType & { items: HistoryGroupType[] }> {
  return (isItem: boolean) => Array.from({ length }, (_, index) => ({
    group_id: index,
    modality: 'CT' as const,
    images: 100,
    series_number: 1,
    series_description: 'Series Description',
    source: 'Source',
    status: 'SUCCEEDED',
    items: isItem ? createHistoryItems(3)(false) : [],
  }))
}

export function createHistoryWorklistMockData(): HistoryWorklistType[] {
  const data = Array.from({ length: 10 }, (_, index) => ({
    list_id: index,
    study_date: '2023-09-12',
    study_description: 'Study Description',
    modality: ['CT', 'RS'] as ModalityType[],
    group: createHistoryItems(index)(true),
  }))

  return data
}

export const historyWorklistMockData = createHistoryWorklistMockData()
