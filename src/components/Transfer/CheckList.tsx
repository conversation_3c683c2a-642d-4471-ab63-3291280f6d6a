import {
  Checkbox, Collapse, ConfigProvider, List,
} from 'antd'
import VirtualList from 'rc-virtual-list'

import { color } from 'src/styles/utils/variables'

import TransferSwitchGroup, { type SwitchGroup } from './TransferSwitchGroup'

type Props<T> = {
  onChecked: (checkedState: RemoteType[]) => void
  dataSource: Array<T>
  checkedState: RemoteType[]
  isSwitch?: boolean
}

type CheckListType = {
  id: string
  key: number
  label: string
}

function CheckList<T extends CheckListType>({
  onChecked, dataSource, checkedState, isSwitch,
}: Props<T>) {
  const handleSwitch = (id: number, switchGroup: SwitchGroup) => {
    const updatedState = checkedState.map((item) => (
      item.id === id
        ? { ...item, ...switchGroup }
        : item
    ))

    onChecked(updatedState)
  }

  const handleCheckboxChange = (id: number) => {
    const isChecked = checkedState.some((item) => item.id === id)
    const updatedState = isChecked
      ? checkedState.filter((item) => item.id !== id)
      : [...checkedState, isSwitch ? { id, rs_transfer: true, ct_transfer: false } : { id }]

    onChecked(updatedState)
  }

  return (
    <ConfigProvider theme={{
      components: {
        Collapse: {
          contentPadding: '.5rem 0',
        },
      },
    }}
    >
      <List
        itemLayout="horizontal"
        className="destination-setting-list"
        split={false}
        style={{ backgroundColor: color.gray[2] }}
      >
        <VirtualList
          data={dataSource}
          height={460}
          itemKey="key"
          style={{ padding: 0 }}
        >
          {(data) => (
            <List.Item style={{ padding: 0, borderRadius: 4 }}>
              {isSwitch ? (
                <Collapse
                  activeKey={checkedState.map((item) => item.id)}
                  onChange={() => handleCheckboxChange(Number(data.key))}
                  destroyInactivePanel
                  items={[{
                    ...data,
                    children: (
                      <TransferSwitchGroup
                        checked={checkedState.find((item) => item.id === Number(data.key))}
                        onChange={(switchGroup) => {
                          handleSwitch(Number(data.key), switchGroup)
                        }}
                      />),
                    style: { padding: 0 },
                    classNames: {
                      header: 'collapsed-header-hover',
                    },
                  }]}
                  bordered={false}
                  // eslint-disable-next-line react/no-unstable-nested-components
                  expandIcon={({ isActive }) => (<Checkbox checked={isActive} />)}
                  style={{
                    width: '100%',
                    padding: '6px .5rem',
                    background: checkedState.some((item) => item.id === data.key) ? color.gray[3] : color.gray[2],
                    borderRadius: 4,
                    transition: 'all .3s',
                  }}
                />
              ) : (
                <Checkbox
                  checked={checkedState.some((item) => item.id === data.key)}
                  onChange={() => handleCheckboxChange(data.key)}
                  className={`
                    checklist-checkbox-item
                    ${checkedState.some((item) => item.id === data.key) ? 'checked' : ''}
                  `}
                >
                  <span className="transition-enter">{data.label}</span>
                </Checkbox>
              )}
            </List.Item>
          )}
        </VirtualList>
      </List>
    </ConfigProvider>
  )
}

export default CheckList
export type { CheckListType }
