import { useEffect, useReducer, useState } from 'react'

import { EyeOutlined } from '@ant-design/icons'
import {
  Button, Flex, List, Modal, Slider, Spin, Tooltip,
} from 'antd'

import { LoadingIcon2 } from 'src/assets/icons'
import { useAntModal } from 'src/hook/useAntModal'
import i18n from 'src/i18n'
import { useGetRsPreviewZipMutation } from 'src/services/api'

type Props = {
  worklistId?: number
  seriesInfo?: PreviewSeriesInfoType
  disabled?: boolean
  classNames?: { readonly button?: string }
}

interface ImageState {
  images: string[]
  total: number
  current: number
}

type Action =
  | { type: 'initImage' }
  | { type: 'setImageData', payload: ImageState }
  | { type: 'setCurrentImageIndex', payload: number }

const initImageState: ImageState = {
  images: [],
  total: 0,
  current: 0,
}

function reducer(state: ImageState, action: Action) {
  switch (action.type) {
    case 'initImage':
      return initImageState
    case 'setImageData':
      return { ...state, ...action.payload }
    case 'setCurrentImageIndex':
      return { ...state, current: action.payload }
    default:
      return state
  }
}

const { demo } = window.config.env

function PreviewModal({
  worklistId, seriesInfo, disabled, classNames,
}: Props) {
  const info = seriesInfo ? Object.entries(seriesInfo).map(([key, value]) => {
    if (key === 'seriesDescription') {
      return value
    }
    return `${key.replace(/([A-Z])/g, '$1').replace(/^./, (str) => str.toUpperCase())}: ${value}`
  }) : []

  // state
  const [isLoading, setIsLoading] = useState(false)
  const [imageState, dispatchImageState] = useReducer(reducer, initImageState)

  // modal
  const modal = useAntModal({
    triggerProps: {
      disabled,
      className: `icon-only ${classNames?.button}`,
      icon: <EyeOutlined />,
    },
    modalProps: {
      title: `${i18n.t('titles.rs')} ${i18n.t('titles.preview')}`,
      onCancel() {
        imageState.images.forEach((url: string) => URL.revokeObjectURL(url))
      },
      className: 'preview-modal',
      styles: {
        header: { margin: 0 },
        body: demo
          ? { padding: '1.5rem 2rem' }
          : { padding: '1.5rem 0' },
        footer: { marginTop: 0 },
      },
      centered: true,
      width: demo ? '90vw' : '800px',
    },
  })

  // api
  const [getRsPreviewMutation] = useGetRsPreviewZipMutation()

  const fetchImages = async () => {
    if (!worklistId) return
    setIsLoading(true)
    const res = await getRsPreviewMutation({ worklist_id: worklistId })
    setIsLoading(false)
    const images = 'data' in res ? res.data : []
    if (images) {
      dispatchImageState({
        type: 'setImageData',
        payload: {
          images,
          total: images.length,
          current: 0,
        },
      })
    }
  }

  // function
  const changeImageState = (delta: number) => {
    const { current, total } = imageState
    const nextIndex = Math.min(Math.max(current + delta, 0), total - 1)

    if (nextIndex !== current) {
      dispatchImageState({ type: 'setCurrentImageIndex', payload: nextIndex })
    }
  }

  const handleWheel = (event: React.WheelEvent<HTMLDivElement>) => {
    changeImageState(event.deltaY > 0 ? 1 : -1)
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    const keyActions: Record<string, number> = {
      ArrowUp: -1, ArrowDown: 1, PageUp: -1, PageDown: 1,
    }

    if (keyActions[event.key] !== undefined) {
      changeImageState(keyActions[event.key])
    }
  }

  useEffect(() => {
    if (!modal.modalProps.open || typeof worklistId === 'undefined') return
    fetchImages()
  }, [modal.modalProps.open])

  const mainWidth = demo ? {
    width: '100%',
    height: '100%',
    minHeight: '720px',
  } : {
    width: '720px',
    aspectRatio: '1 / 1',
  }

  return (
    <div
      role="presentation"
      onWheel={handleWheel}
      onKeyDown={handleKeyDown}
    >
      <Tooltip title={i18n.t('tooltips.worklist_preview')}>
        <Button {...modal.triggerProps} />
      </Tooltip>
      <Modal
        {...modal.modalProps}
        footer={(
          <Button
            style={{ lineHeight: '1.25rem', cursor: 'pointer' }}
            onClick={modal.modalProps.onCancel}
          >
            {i18n.t('buttons.close')}
          </Button>
        )}
      >
        <Flex
          align="center"
          justify="center"
          style={{
            position: 'relative',
            margin: 'auto',
            background: 'black',
            ...mainWidth,
          }}
        >
          {isLoading && (
            <Flex
              align="center"
              justify="center"
              style={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                background: 'rgba(0,0,0)',
              }}
            >
              <Spin indicator={(<LoadingIcon2 className="spin-animation" />)} />
            </Flex>
          )}

          {imageState.images.length > 0 ? (
            <>
              <List
                itemLayout="horizontal"
                dataSource={info}
                renderItem={(item) => (
                  <List.Item style={{ padding: 0 }}>{item}</List.Item>
                )}
                split={false}
                style={{
                  position: 'absolute',
                  top: 8,
                  left: 8,
                }}
              />
              <img
                src={imageState.images[imageState.current]}
                alt={`${imageState.current + 1} images`}
                draggable="false"
                style={{
                  height: '75vh',
                  minHeight: '256px',
                  maxHeight: '85%',
                  maxWidth: '85%',
                  userSelect: 'none',
                }}
              />
              <Flex
                vertical
                align="end"
                gap="middle"
                style={{
                  position: 'absolute',
                  padding: '1rem 0',
                  right: 8,
                  height: '100%',
                }}
              >
                <p>{imageState.current + 1}/{imageState.total}</p>
                <Slider
                  vertical
                  reverse
                  keyboard={false}
                  min={1}
                  max={imageState.total}
                  value={imageState.current + 1}
                  onChange={(value) => {
                    dispatchImageState({ type: 'setCurrentImageIndex', payload: value - 1 })
                  }}
                  style={{
                    flex: 1,
                  }}
                />
              </Flex>
            </>
          ) : (<> {i18n.t('error_contents.no_data')} </>)}
        </Flex>
      </Modal>
    </div>

  )
}

export default PreviewModal
