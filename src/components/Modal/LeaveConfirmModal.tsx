import { useCallback } from 'react'

import { Modal } from 'antd'
import { useBlocker } from 'react-router'

import { useAntModal } from 'src/hook/useAntModal'
import i18n from 'src/i18n'

interface Props { condition: boolean }
function LeaveConfirmModal({ condition }: Props) {
  const modal = useAntModal({
    modalProps: {
      title: i18n.t('modal_titles.leave_page_confirmation'),
      okText: i18n.t('buttons.leave'),
      style: { maxWidth: '21.75rem' },
      styles: {
        body: { padding: '4.25rem 1.25rem' },
      },
      centered: true,
      destroyOnClose: true,
    },
  })

  const blocker = useBlocker(
    useCallback(() => condition, [condition]),
  )

  const handleConfirm = () => {
    if (blocker.state === 'blocked') {
      blocker.proceed()
    }
  }

  const handleCancel = () => {
    if (blocker.state === 'blocked') {
      blocker.reset()
    }
  }

  return (
    <Modal
      {...modal.modalProps}
      open={blocker.state === 'blocked'}
      onOk={handleConfirm}
      onCancel={handleCancel}
    >
      {i18n.t('modal_contents.leave_page_confirmation')}
    </Modal>
  )
}

export default LeaveConfirmModal
