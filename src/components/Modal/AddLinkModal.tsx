import { Dispatch, SetStateAction, useState } from 'react'

import {
  Checkbox, Col, ConfigProvider, Flex, Form, Input, Modal, Radio, RadioChangeEvent, Row, Space,
} from 'antd'

import i18n from 'src/i18n'
import { color } from 'src/styles/utils/variables'

import modalFooterButtonProps from './modalFooterButtonProps'

interface Props {
  modalLinkBtn: (account?: string, password?: string) => void
  openModal: boolean
  setOpenModal: Dispatch<SetStateAction<boolean>>;
}

function AddLinkModal({
  modalLinkBtn,
  openModal,
  setOpenModal,
}: Props) {
  const [form] = Form.useForm()
  const [disabled, setDisabled] = useState<boolean>(true)

  const onChange = (e: RadioChangeEvent) => {
    const radioValue = e.target.value === 'visitor'
    setDisabled(radioValue)
  }

  const handleOk = async () => {
    try {
      await form.validateFields()
      // if visitor then don't send account and password
      if (disabled) {
        modalLinkBtn()
      } else {
        const { account, password } = form.getFieldsValue()
        modalLinkBtn(account, password)
      }
      form.resetFields()
      setOpenModal(false)
    } catch (e) {
      console.error(e)
    }
  }
  const handleCancel = async () => {
    form.resetFields()
    setOpenModal(false)
  }

  return (
    <ConfigProvider theme={{
      components: {
        Radio: {
          colorPrimary: color.gray[0].default,
          colorBgContainer: 'transparent',
        },
      },
    }}
    >
      <Modal
        title={i18n.t('modal_titles.link_to', { value: i18n.t('titles.folder'), joinArrays: ' ' })}
        okText={i18n.t('buttons.link')}
        open={openModal}
        onOk={handleOk}
        onCancel={handleCancel}
        okButtonProps={{
          style: { width: 100 },
          ...modalFooterButtonProps.okButtonProps,
        }}
        cancelButtonProps={{
          style: { width: 100 },
          ...modalFooterButtonProps.cancelButtonProps,
        }}
        classNames={{
          body: 'add-list-modal-body',
        }}
      >
        <Row justify="center" gutter={16}>
          <Col>
            <p>{i18n.t('paragraphs.connection_identity')}</p>
          </Col>
          <Col>
            <Radio.Group onChange={onChange} defaultValue="visitor" id="add-list-modal-radio">
              <Space direction="vertical" align="start">
                <Radio value="visitor">{i18n.t('radios.visitor')}</Radio>
                <Radio value="registeredUser">{i18n.t('radios.registered_user')}</Radio>
              </Space>
            </Radio.Group>
          </Col>
        </Row>
        <Form
          form={form}
          layout="horizontal"
          style={{ width: '100%', padding: '0 3.5rem', marginTop: '20px' }}
          disabled={disabled}
        >
          <Form.Item
            label={i18n.t('titles.account')}
            name="account"
            labelCol={{ span: 12 }}
            validateTrigger="onSubmit"
            rules={disabled ? [] : [
              {
                required: true,
                message: i18n.t('form_rules.enter_variable', {
                  variable: i18n.t('plain_texts.account'),
                  joinArrays: ' ',
                }),
              },
            ]}
          >
            <Input placeholder={i18n.t('titles.account')} />
          </Form.Item>
          <Form.Item
            label={i18n.t('titles.password')}
            name="password"
            labelCol={{ span: 12 }}
            validateTrigger="onSubmit"
            rules={disabled ? [] : [
              {
                required: true,
                message: i18n.t('form_rules.enter_variable', {
                  variable: i18n.t('plain_texts.password'),
                  joinArrays: ' ',
                }),
              },
            ]}
          >
            <Input.Password placeholder={i18n.t('titles.password')} />
          </Form.Item>
          <Form.Item
            name="remember"
            valuePropName="checked"
            style={{ marginTop: '3rem', marginBottom: '-1rem' }}
          >
            <Flex justify="center">
              <Checkbox>{i18n.t('checkboxes.remember_me')}</Checkbox>
            </Flex>
          </Form.Item>
        </Form>
      </Modal>
    </ConfigProvider>
  )
}

export default AddLinkModal
