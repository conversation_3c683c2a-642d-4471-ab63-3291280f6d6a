import React from 'react'

import { Button } from 'antd'

import {
  AlertCircleRed, AlertTriangle, SuccessCircle, AlertInfoCircle, XSmIcon,
} from 'src/assets/icons'
import i18n from 'src/i18n'
import 'src/styles/components/notice.css'

interface Props {
  id: number
  type: string
  patient_id?: string
  msg: string
  time: string
  handleClear: (notify_id: number) => void
}
type Card = {
  [key: string]: React.ReactNode
}

function Notice({
  id, type, patient_id, msg, time, handleClear,
}: Props) {
  const title = (): string => {
    let titleKey
    switch (type) {
      case 'system disconnected':
        titleKey = 'titles.system_disconnected'
        break
      case 'exceeds file limit':
        titleKey = 'titles.exceeds_file_limit'
        break
      default:
        titleKey = 'titles.system_maintenance'
    }

    return i18n.t(titleKey)
  }

  const icon: Card = {
    'inference successed': <SuccessCircle />,
    'inference failed': <AlertTriangle />,
    'inference timeout': <AlertCircleRed width={28} height={28} />,
    'system disconnected': <AlertCircleRed width={28} height={28} />,
    'exceeds file limit': <AlertInfoCircle />,
  }

  return (
    <section className="notice-card">
      <Button
        size="small"
        className="close-btn"
        onClick={() => handleClear(id)}
      > <XSmIcon />
      </Button>
      <main className="card-body">
        <div className="card-icon-box">
          {icon[type]}
        </div>
        <div className="card-content">
          {patient_id ? (
            <hgroup>
              <h5>{i18n.t('titles.patient_id')}</h5>
              <p>{patient_id}</p>
            </hgroup>
          ) : title()}
          <div className="card-text">
            {msg}
          </div>
        </div>
      </main>
      <footer className="card-footer">
        {time}
      </footer>
    </section>
  )
}

export default Notice
