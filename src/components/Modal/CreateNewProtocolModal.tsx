import { useState, useRef, useEffect } from 'react'

import {
  Button, ConfigProvider, Flex, Form, Input, Modal,
} from 'antd'
import { useNavigate } from 'react-router'

import { NewProtocolModaTable } from 'src/components/Table'
import { useAntModal } from 'src/hook/useAntModal'
import i18n from 'src/i18n'
import { useCheckProtocolNameMutation } from 'src/services/api'
import { color } from 'src/styles/utils/variables'
import { requiredRules } from 'src/utils/verify'
import 'src/styles/components/modal.css'

type Props = {
  disabled: boolean
}

function CreateNewProtocolModal({ disabled }: Props) {
  const [form] = Form.useForm()

  const [copyProtocolID, setCopyProtocolID] = useState<number | string>('')
  const [protocolName, setProtocolName] = useState<string>()
  const [currentPage, setCurrentPage] = useState<1 | 2>(1)
  const [errorText, setErrorText] = useState<string>('')
  const [isVerify, setIsVerify] = useState({ check: false, text: '' })
  const radioRefs: { current: { [key: string]: HTMLInputElement | null } } = useRef({})
  const [checkProtocolNameMutation] = useCheckProtocolNameMutation()
  const navigate = useNavigate()

  // modal
  const createModal = useAntModal({
    triggerProps: {
      disabled,
    },
    modalProps: {
      title: i18n.t('modal_titles.create_new_protocol'),
      okText: i18n.t('buttons.next'),
      width: '765px',
      className: 'create-new-modal',
      styles: {
        header: {
          padding: '0 30px',
        },
        body: {
          height: '70vh',
          minHeight: 600,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        },
      },
    },
  })
  const leaveModal = useAntModal({
    modalProps: {
      title: i18n.t('modal_titles.leave_page_confirmation'),
      okText: i18n.t('buttons.leave'),
      onOk: async () => {
        createModal.dismiss()
        setCurrentPage(1)
        form.resetFields()
      },
      className: 'confirm-modal',
      centered: true,
    },
  })

  const requiredModal = useAntModal({
    modalProps: {
      centered: true,
      className: 'confirm-modal',
      okText: i18n.t('buttons.ok'),
      footer: null,
      styles: {
        body: { padding: '7.5rem 2.5rem' },
      },
    },
  })

  const handleRadioChange = (value: number | string) => {
    setCopyProtocolID(value)
  }

  const handleCancel = () => {
    if (currentPage === 1) {
      createModal.dismiss()
      setCurrentPage(1)
    } else {
      leaveModal.trigger()
      setCopyProtocolID('')
    }
    setIsVerify({ check: false, text: '' })
    form.resetFields()
  }

  const handleSecondPage = async () => {
    navigate('create', { state: { protocolName, copyProtocolID } })
    setCurrentPage(1)
  }

  const handleFirstPageError = (e: any) => {
    if (typeof e === 'object' && e !== null) {
      let text: string = i18n.t('form_rules.enter_variable', {
        variable: i18n.t('plain_texts.protocol_name'),
        joinArrays: ' ',
      })
      if ('status' in e) {
        text = e.status === 409
          ? i18n.t('form_rules.variable_unique', {
            variable: i18n.t('titles.protocol_name'),
            joinArrays: ' ',
          })
          : i18n.t('error_contents.server_error')
      }
      setIsVerify({ check: true, text })
    }
  }

  const handleFirstPage = async () => {
    try {
      await checkProtocolNameMutation({ name: form.getFieldsValue().protocolName }).unwrap()
      setProtocolName(form.getFieldsValue().protocolName)
      setCurrentPage(2)
    } catch (e) {
      handleFirstPageError(e)
    }
  }

  const handleError = () => {
    setErrorText(i18n.t('error_contents.choose_protocol'))
    requiredModal.trigger()
  }

  const handleFinish = async () => {
    try {
      await form.validateFields()
      if (currentPage === 1) {
        await handleFirstPage()
      } else {
        await handleSecondPage()
      }
    } catch (e) {
      handleError()
    }
  }

  useEffect(() => {
    if (!createModal.modalProps.open) {
      return () => {
        form.resetFields()
      }
    }
    return () => { }
  }, [createModal.modalProps.open])

  return (
    <>
      <ConfigProvider theme={{
        components: {
          Table: {
            borderColor: 'rgba(192, 192, 192, 0.30)',
            headerBg: color.gray[3],
            colorBgContainer: color.gray[3],
            rowHoverBg: color.gray[1].default,
            rowSelectedHoverBg: color.gray[1].default,
          },
        },
      }}
      >

        <Button
          htmlType="button"
          className="gray"
          disabled={disabled}
          {...createModal.triggerProps}
        >
          {i18n.t('buttons.new_protocol')}
        </Button>
        <Modal
          {...createModal.modalProps}
          onCancel={handleCancel}
          footer={[
            <Button
              htmlType="button"
              variant="outlined"
              color="primary"
              onClick={handleCancel}
              key="cancel"
            >
              {i18n.t('buttons.cancel')}
            </Button>,
            <Button
              htmlType="submit"
              variant="outlined"
              color="primary"
              onClick={handleFinish}
              key="next"
            >
              {i18n.t('buttons.next')}
            </Button>,
          ]}
        >
          <Form
            form={form}
            colon={false}
            name="create-protocol-form"
            layout="vertical"
            requiredMark={false}
            onFinish={handleFinish}
          >
            {
              currentPage === 1
              && (
                <Form.Item
                  label={<span>{i18n.t('titles.create_protocol_name')}</span>}
                  name="protocolName"
                  htmlFor="protocolName"
                  layout="vertical"
                  validateStatus={isVerify.check ? 'error' : undefined}
                  help={isVerify.text}
                  rules={[requiredRules()]}
                  style={{ width: '35%', minWidth: 300, textAlign: 'center' }}
                >
                  <Input
                    id="protocolName"
                    placeholder={i18n.t('form_placeholders.enter_variable', {
                      variable: i18n.t('titles.protocol_name'),
                      joinArrays: ' ',
                    })}
                    style={{ textAlign: 'center' }}
                    maxLength={16}
                  />
                </Form.Item>
              )
            }
            {
              currentPage === 2
              && (
                <Form.Item
                  name="radio-group"
                  className="radio-group"
                  style={{ marginBottom: 0, width: '100%' }}
                  rules={[requiredRules()]}
                >
                  <main id="radio-group" style={{ height: '100%' }}>
                    <Flex vertical justify="center" align="center">
                      <div>
                        <label htmlFor="create-protocol" className="radio-button">
                          <span>
                            <input
                              id="create-protocol"
                              type="radio"
                              name="radio-group"
                              onChange={(e) => handleRadioChange(e.target.id)}
                              ref={(input) => { radioRefs.current['create-protocol'] = input }}
                            />
                            <span />
                          </span>
                          <span className="radio-button-text">
                            {i18n.t('paragraphs.create_new_protocol.first')}
                          </span>
                        </label>
                      </div>
                      <p>{i18n.t('paragraphs.create_new_protocol.second')}</p>
                      <h3>{i18n.t('paragraphs.create_new_protocol.third')}</h3>

                    </Flex>
                    <NewProtocolModaTable
                      copyProtocolID={copyProtocolID}
                      setCopyProtocolID={setCopyProtocolID}
                      radioRefs={radioRefs}
                    />
                  </main>
                </Form.Item>
              )
            }
          </Form>
        </Modal>
      </ConfigProvider>

      {/* leave modal */}
      <Modal {...leaveModal.modalProps}>
        {i18n.t('modal_contents.leave_page_confirmation')}
      </Modal>

      {/* required modal */}
      <Modal {...requiredModal.modalProps}>
        {errorText}
      </Modal>
    </>
  )
}

export default CreateNewProtocolModal
