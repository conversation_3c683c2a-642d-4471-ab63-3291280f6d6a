/* eslint-disable max-len */
import { SVGProps } from 'react'

import { color } from 'src/styles/utils/variables'

type ColorLabelProps = SVGProps<SVGSVGElement> & {
  half?: boolean
}
const stroke = color.gray[1].default
function FullLabel() {
  return (
    <>
      <path d="M0 0.999999C0 0.447714 0.447715 0 1 0H14.471C14.8019 0 15.1114 0.163731 15.2977 0.43731L19.1375 6.07852C19.3582 6.40284 19.369 6.82629 19.1649 7.16136L15.2922 13.5202C15.1108 13.8181 14.7871 14 14.4382 14H1C0.447715 14 0 13.5523 0 13V6.22222V0.999999Z" fill="currentColor" />
      <path d="M1 0.5H14.471C14.6365 0.5 14.7912 0.581865 14.8843 0.718655L18.7241 6.35986C18.8345 6.52202 18.8399 6.73375 18.7378 6.90128L14.8652 13.2601C14.7745 13.4091 14.6126 13.5 14.4382 13.5H1C0.723858 13.5 0.5 13.2761 0.5 13V6.22222V0.999999C0.5 0.723857 0.723857 0.5 1 0.5Z" stroke={stroke} strokeOpacity="0.75" />
    </>
  )
}
function HalfLabel() {
  return (
    <>
      <path d="M14.8134 13.3267L1.16805 0.656042C1.10406 0.596627 1 0.642005 1 0.729321V13.4C1 13.4552 1.04477 13.5 1.1 13.5H14.7453C14.8362 13.5 14.88 13.3886 14.8134 13.3267Z" fill="currentColor" />
      <path d="M0.5 0.999999C0.5 0.723857 0.723857 0.5 1 0.5H14.471C14.6365 0.5 14.7912 0.581866 14.8843 0.718654L18.7241 6.35986C18.8345 6.52203 18.8399 6.73375 18.7379 6.90128L14.8652 13.2601C14.7745 13.4091 14.6126 13.5 14.4382 13.5H1C0.723858 13.5 0.5 13.2761 0.5 13V6.22222V0.999999Z" stroke={stroke} />
    </>
  )
}

function ColorLabel({ half = false, ...props }: ColorLabelProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 20 14" fill="none" {...props}>
      {half ? <HalfLabel /> : <FullLabel />}
    </svg>
  )
}

export default ColorLabel
