import { ConfigProvider, Table } from 'antd'
import type { ColumnsType, SorterResult } from 'antd/es/table/interface'

import { LoadingIcon2, PriorityIcon } from 'src/assets/icons'
import ProgressBar from 'src/components/ProgressBar'
import i18n from 'src/i18n'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { updateWorklistFocus } from 'src/store/reducers/worklistSlice'
import { color } from 'src/styles/utils/variables'
import { WorkStatusEnum } from 'src/utils/enum'
import { formattedStatus, withoutTime } from 'src/utils/helper'

interface Props {
  tableLoading: boolean
  history: boolean
  sortedInfo: SorterResult<WorklistGroupType>
  handleDataChange: (page?: number, sorter?: any) => void
}

function WorklistGroupTable({
  tableLoading, history, sortedInfo, handleDataChange,
}: Props) {
  const { worklistGroup, worklistGroupFocus, worklistGroupPage } = useAppSelector((state) => state.worklistReducer)
  const dispatch = useAppDispatch()

  const columns: ColumnsType<WorklistGroupType> = [
    {
      dataIndex: 'insert',
      key: 'insert',
      width: '25px',
      render: (_, { insert }) => (
        <span>
          {insert && (
            <div className="icon-box">
              <PriorityIcon width={24} />
            </div>
          )}
        </span>
      ),
    },
    {
      title: i18n.t('titles.patient_id'),
      dataIndex: 'patient_id',
      key: 'patient_id',
      width: '100px',
      sorter: history,
      sortOrder: sortedInfo.columnKey === 'patient_id' ? sortedInfo.order : null,
      showSorterTooltip: false,
      ellipsis: true,
    },
    {
      title: i18n.t('titles.study_date'),
      dataIndex: 'study_date',
      key: 'study_date',
      width: '100px',
      sorter: history,
      sortOrder: sortedInfo.columnKey === 'study_date' ? sortedInfo.order : null,
      showSorterTooltip: false,
      render: (_, { study_date }) => (
        <p style={{
          minWidth: 90,
          color: color.gray[1].default,
        }}
        >{withoutTime(study_date)}
        </p>
      ),
    },
    {
      title: i18n.t('titles.study_status'),
      dataIndex: 'study_status',
      key: 'study_status',
      width: '100px',
      sorter: history,
      sortOrder: sortedInfo.columnKey === 'study_status' ? sortedInfo.order : null,
      showSorterTooltip: false,
      render: (value, { progress }) => {
        const studyStatus: string[] = [WorkStatusEnum.PROCESSING, WorkStatusEnum.PENDING, WorkStatusEnum.SUSPENDED]
        if (studyStatus.includes(value)) {
          return (
            <ProgressBar
              status={value}
              progress={progress * 100}
            />
          )
        }
        return (
          <div className="progress-title">
            {formattedStatus(value)}
            {/* {protocolName} */}
          </div>
        )
      },
    },
  ]

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            borderColor: color.gray[1].variants,
          },
        },
      }}
    >
      <Table
        columns={columns}
        dataSource={worklistGroup}
        loading={{
          spinning: tableLoading,
          delay: 500,
          indicator: <LoadingIcon2 className="spin-animation" />,
        }}
        pagination={{
          current: worklistGroupPage.current,
          total: worklistGroupPage.total,
          pageSize: worklistGroupPage.size,
          showSizeChanger: false,
          position: ['bottomCenter'],
        }}
        rowKey={(record) => record.id}
        onRow={(record) => ({ onClick: () => { dispatch(updateWorklistFocus({ id: record.id })) } })}
        rowClassName={(record) => {
          if (worklistGroupFocus.id) {
            return record.id === worklistGroupFocus.id ? 'selected-row' : ''
          }
          return ''
        }}
        style={{ height: '100%' }}
        className="study-table-container"
        onChange={(pagination, _filters, sorter, extra) => {
          if (extra.action === 'paginate' && pagination.current) {
            handleDataChange(pagination.current, undefined)
          } else if (extra.action === 'sort') {
            handleDataChange(undefined, sorter)
          }
        }}
      />

    </ConfigProvider>
  )
}

export default WorklistGroupTable
