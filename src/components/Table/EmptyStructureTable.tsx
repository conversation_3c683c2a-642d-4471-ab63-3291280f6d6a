import {
  Form, Input, Select, Table,
} from 'antd'
import type { ColumnsType } from 'antd/es/table/interface'

import { XCircleIcon } from 'src/assets/icons'
import Button from 'src/components/Button'
import { TableInput } from 'src/components/Form'
import { ModalBtn } from 'src/components/Modal'
import ColorPicker from 'src/components/Popup/ColorPicker'
import i18n from 'src/i18n'
import { useAppSelector } from 'src/store/hook'
import { requiredRules, duplicatedRules } from 'src/utils/verify'

import 'src/styles/components/ListLayout.css'

interface Props {
  dataSource: CustomizedStructuresType[]
  onAdd: (data: CustomizedStructuresType) => void
  onDelete: (data: CustomizedStructuresType) => void
  onChange: (
    id: number,
    fieldName: string,
    data: CustomizedStructuresType | CustomizedStructuresChangeDataType
  ) => void
}

function EmptyStructureTable({
  dataSource,
  onAdd, onDelete, onChange,
}: Props) {
  const [form] = Form.useForm()
  const { customizedStructureType } = useAppSelector((state) => state.configReducer)

  const onFinish = async () => {
    try {
      await form.validateFields()
    } catch (e) {
      return
    }
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { name, type, color_code } = form.getFieldsValue()
    onAdd({
      id: Date.now(),
      show: true,
      name,
      type,
      color_code: typeof color_code === 'string' ? color_code : `#${color_code.toHex()}`,
      sort: null,
      structure_operations: [],
      mode: '',
      margin_value: null,
      margin_symmetric: true,
    })
    form.resetFields()
  }

  const columns: ColumnsType<CustomizedStructuresType> = [
    {
      title: (
        <Form.Item
          label={i18n.t('titles.structure_id')}
          name="name"
          labelCol={{ span: 24 }}
          required
          rules={[requiredRules('name'), duplicatedRules('name', dataSource)]}
        >
          <Input placeholder={
            i18n.t('form_placeholders.enter_variable', {
              variable: i18n.t('titles.structure_id'),
              joinArrays: ' ',
            })
          }
          />
        </Form.Item>
      ),
      dataIndex: 'name',
      key: 'name',
      width: 250,
      render: (_, { id, name }) => {
        const duplication = dataSource.some(
          (item) => item.id !== id && item.name && item.name === name,
        )
        return (
          <TableInput
            id={id}
            name="name"
            value={name}
            handleChange={onChange}
            required
            duplication={duplication}
          />
        )
      },
    },
    {
      title: (
        <Form.Item
          label={i18n.t('titles.type')}
          name="type"
          labelCol={{ span: 24 }}
          required
          rules={[requiredRules('type')]}
        >
          <Select
            showSearch
            style={{ width: '100%' }}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            placeholder={i18n.t('form_placeholders.tree_select')}
            allowClear
            options={customizedStructureType.map((item) => ({ title: item, value: item }))}
          />
        </Form.Item>
      ),
      dataIndex: 'type',
      key: 'type',
      render: (_, { id, type }) => (
        <Select
          showSearch
          value={type}
          dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
          placeholder={i18n.t('form_placeholders.tree_select')}
          onChange={(data: string) => {
            if (id !== undefined) {
              onChange(id, 'type', data)
            }
          }}
          options={customizedStructureType.map((item) => ({ label: item, value: item }))}
          className="add-empty-list-item-type"
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: (
        <Form.Item
          label={i18n.t('titles.color_code')}
          name="color_code"
          labelCol={{ span: 24 }}
        >
          <ColorPicker />
        </Form.Item>
      ),
      dataIndex: 'color_code',
      key: 'color_code',
      width: 70,
      render: (_, { id, color_code }) => (
        <ColorPicker
          key={`color-picker-${id}`}
          value={color_code}
          onChange={(value) => {
            if (id === undefined) return
            onChange(id, 'color_code', typeof value === 'string' ? value : `#${value.toHex()}`)
          }}
        />
      ),
    },
    {
      title: (
        <Button
          type="submit"
          className="outline"
          style={{ marginTop: 40, height: '32px', lineHeight: 0 }}
        >
          {i18n.t('buttons.add')}
        </Button>
      ),
      width: 65,
      render: (_, record) => (
        <ModalBtn
          btnClass="ant-list-delete"
          btnName={<XCircleIcon />}
          modalTitle={i18n.t('modal_titles.delete_confirmation')}
          modalClassName="confirm-modal"
          okText={i18n.t('buttons.delete')}
          onOk={() => { onDelete(record) }}
        >
          {i18n.t('modal_contents.delete_confirmation', { item: `"${record.name}"`, joinArrays: ' ' })}
        </ModalBtn>
      ),
    },
  ]

  return (
    <Form
      form={form}
      name="EmptyStructure"
      onFinish={onFinish}
      initialValues={{ color_code: '#039BA0' }}
    >
      <Table
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        className="add-newRow-table"
        style={{ maxWidth: '990px' }}
      />
    </Form>
  )
}

export default EmptyStructureTable
