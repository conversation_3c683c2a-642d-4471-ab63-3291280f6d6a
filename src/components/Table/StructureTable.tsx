import { Input, Table } from 'antd'
import type { ColumnsType } from 'antd/es/table/interface'
import { isString } from 'lodash-es'
import trim from 'lodash-es/trim'

import { LoadingIcon2 } from 'src/assets/icons'
import DebouncedWithHOC from 'src/components/DebouncedWithHOC'
import i18n from 'src/i18n'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { updateStructure } from 'src/store/reducers/structureSlice'

import ColorPicker from '../Popup/ColorPicker'

interface Props {
  tableLoading: boolean
}

function StructureTable({ tableLoading }: Props) {
  const { structures } = useAppSelector((state) => state.structureReducer)
  const dispatch = useAppDispatch()

  const columns: ColumnsType<StructureType> = [
    {
      title: i18n.t('titles.efai_structure_name'),
      dataIndex: 'efai_structure_name',
      key: 'efai_structure_name',
      width: '210px',
    },
    {
      title: i18n.t('titles.customized_name'),
      dataIndex: 'customized_name',
      key: 'customized_name',
      width: '210px',
      render(defaultValue, record) {
        const text = trim((defaultValue as string))
        const efaiNameHasUnique = structures
          .find(({ efai_structure_name }) => (text === efai_structure_name))
        const CustomizedNameHasUnique = structures
          .find(({ id, customized_name }) => {
            if (record.id !== id && !!customized_name) {
              return (text === customized_name)
            }
            return false
          })

        const isError: boolean = !!CustomizedNameHasUnique || !!efaiNameHasUnique

        return (
          <DebouncedWithHOC
            onChange={(value) => {
              dispatch(updateStructure({ id: record.id, customized_name: value }))
            }}
            defaultValue={defaultValue ? `${defaultValue}` : ''}
          >
            <Input
              id={`customized_name-${record.id}`}
              status={isError ? 'error' : undefined}
              maxLength={16}
              placeholder={i18n.t('form_placeholders.enter_variable', {
                variable: i18n.t('titles.name'),
                joinArrays: ' ',
              })}
              className="input-style-default"
              style={{ minWidth: '180px' }}
            />
          </DebouncedWithHOC>
        )
      },
    },
    {
      title: i18n.t('titles.color_code'),
      dataIndex: 'color_code',
      key: 'color_code',
      width: '110px',
      render(value, record) {
        return (
          <ColorPicker
            value={value}
            onChangeComplete={(col) => dispatch(updateStructure({ id: record.id, color_code: col.toHexString() }))}
          />
        )
      },
    },
    {
      title: i18n.t('titles.definition'),
      dataIndex: 'definition',
      key: 'definition',
      render(text, record) {
        return (
          <DebouncedWithHOC
            onChange={(value) => {
              dispatch(updateStructure({ id: record.id, definition: value }))
            }}
            defaultValue={isString(text) ? `${text}` : ''}
          >
            <Input.TextArea
              autoSize
              id={`definition-${record.id}`}
              placeholder={i18n.t('form_placeholders.enter_variable', {
                variable: i18n.t('titles.definition'),
                joinArrays: ' ',
              })}
              className="input-none-style "
              style={{ padding: '.25rem .5rem', minWidth: '300px' }}
            />
          </DebouncedWithHOC>
        )
      },
    },
  ]

  return (
    <section style={{ flex: '1 0 0', position: 'relative' }}>
      <Table
        columns={columns}
        dataSource={structures}
        loading={{
          spinning: tableLoading,
          delay: 500,
          indicator: <LoadingIcon2 className="spin-animation" />,
        }}
        className="setting-table-container"
        pagination={false}
        rowKey={(record) => record.id}
        scroll={{ x: true, y: 'calc(100% - 45px)' }}
      />
    </section>
  )
}

export default StructureTable
