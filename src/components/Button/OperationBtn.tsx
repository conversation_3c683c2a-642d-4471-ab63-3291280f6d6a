import { useState } from 'react'

import {
  <PERSON><PERSON>, <PERSON>lex, <PERSON>dal, <PERSON><PERSON><PERSON>,
} from 'antd'
import { useNavigate } from 'react-router'

import { DownloadIcon, PenToolIcon, ResendIcon } from 'src/assets/icons'
import TransferTabs from 'src/components/Transfer/TransferTabs'
import useAlert from 'src/hook/useAlert'
import { useAntModal } from 'src/hook/useAntModal'
import i18n from 'src/i18n'
import { useGetDicomMutation, useGetRemoteConfigMutation, usePostRsResendMutation } from 'src/services/api'
import { useAppSelector, useAppDispatch } from 'src/store/hook'
import { clearResendRemote, updateResendRemote } from 'src/store/reducers/worklistSlice'
import { color } from 'src/styles/utils/variables'
import { WorkStatusEnum } from 'src/utils/enum'
import { verifyDetailState } from 'src/utils/verify'

import PreviewModal from '../Modal/PreviewModal'
import TransferSwitchGroup, { type SwitchGroup } from '../Transfer/TransferSwitchGroup'

import 'src/styles/components/modal.css'

interface Props {
  worklistId?: number
  seriesInfo?: PreviewSeriesInfoType
  status?: string
}

const initalDownloadSwitchState = {
  rs_transfer: true,
  ct_transfer: false,
}

function OperationBtn({ worklistId, seriesInfo, status = WorkStatusEnum.SUCCEEDED }: Props) {
  // state
  const { resendRemote } = useAppSelector((state) => state.worklistReducer)

  const [downloadSwitch, setDownloadSwitch] = useState<SwitchGroup>(initalDownloadSwitchState)
  const dispatch = useAppDispatch()
  const chooseCount = resendRemote.destination.folder.length + resendRemote.destination.remote_server.length

  // hook
  const [getDicomMutation] = useGetDicomMutation()
  const [getRemoteConfigMutation] = useGetRemoteConfigMutation()
  const [postRsResendMutation] = usePostRsResendMutation()
  const navigate = useNavigate()
  const handleAlert = useAlert()

  // handle modal ok
  const handleDownloadData = async () => {
    if (!worklistId) {
      throw new Error('worklistId not found')
    }
    try {
      await getDicomMutation({
        id: worklistId,
        rs: downloadSwitch.rs_transfer,
        ct: downloadSwitch.ct_transfer,
      }).unwrap()
    } catch (e) {
      handleAlert({ title: i18n.t('error_titles.download'), content: (e as Err).data?.detail }, 'Msg', 'error')
      throw new Error(i18n.t('error_titles.download'))
    } finally {
      setDownloadSwitch(initalDownloadSwitchState)
    }
  }

  // modal
  const resendModal = useAntModal({
    triggerProps: {
      className: 'icon-only',
      icon: <ResendIcon />,
      onClick: async () => {
        try {
          await getRemoteConfigMutation().unwrap()
        } catch (e) {
          handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
        }
      },
    },
    modalProps: {
      onCancel: async () => {
        dispatch(clearResendRemote())
      },
      async onOk(_, setConfirmLoading) {
        if (!worklistId || chooseCount === 0) {
          dispatch(clearResendRemote())
          setConfirmLoading(false)
          throw new Error('Choose at least one destination')
        }
        try {
          await postRsResendMutation({
            id: worklistId,
            remote_server_id_list: resendRemote.destination.remote_server,
            folder_id_list: resendRemote.destination.folder,
          }).unwrap()
          dispatch(clearResendRemote())
        } catch (e) {
          handleAlert({ title: i18n.t('error_titles.resend'), content: (e as Err).data?.detail }, 'Msg', 'error')
          throw new Error((e as Err).data?.detail)
        }
      },
      okText: i18n.t('buttons.send'),
      className: 'resend-modal',
      styles: {
        body: { padding: '3rem 3.75rem' },
      },
      width: '62.5rem',
      confirmLoading: true,
    },
  })
  const downloadModal = useAntModal({
    modalProps: {
      onOk: handleDownloadData,
      onCancel: () => setDownloadSwitch(initalDownloadSwitchState),
      title: i18n.t('buttons.download'),
      okText: i18n.t('buttons.download'),
      confirmLoading: true,
      destroyOnClose: true,
      styles: {
        body: { padding: '1.5rem 2rem' },
      },
    },
  })

  // function
  const handleUpdateWorklistRemote = (
    type: SourceDestinationKey,
    remote: RemoteCategoryKey,
    value: RemoteType[],
  ) => dispatch(updateResendRemote({ type, remote, value }))

  return (
    <>
      <Flex component="nav" gap={8} className="icons-btn-group">
        <PreviewModal
          disabled={status !== WorkStatusEnum.SUCCEEDED}
          worklistId={worklistId}
          seriesInfo={seriesInfo}
        />
        <Tooltip title={i18n.t('tooltips.worklist_redraw')}>
          <Button
            className="basic-shadow icon-only "
            size="small"
            onClick={() => navigate(`${worklistId}`)}
            style={{ width: 32, height: 32 }}
            icon={<PenToolIcon style={{ fontSize: 24 }} />}
          />
        </Tooltip>
        <Tooltip title={i18n.t('tooltips.worklist_resend')}>
          <Button
            className="basic-shadow icon-only "
            size="small"
            disabled={([WorkStatusEnum.REMOVED, WorkStatusEnum.NO_MATCH] as string[]).includes(status)}
            style={{ width: 32, height: 32 }}
            {...resendModal.triggerProps}
          />
        </Tooltip>
        <Tooltip title={i18n.t('tooltips.worklist_download')}>
          <Button
            className="basic-shadow icon-only "
            disabled={([WorkStatusEnum.REMOVED, WorkStatusEnum.NO_MATCH] as string[]).includes(status)}
            onClick={downloadModal.trigger}
            icon={<DownloadIcon />}
          />
        </Tooltip>
      </Flex>
      {/* Resend Modal */}
      <Modal
        {...resendModal.modalProps}
        okButtonProps={{
          variant: 'outlined',
          color: 'primary',
          disabled: verifyDetailState.destination(resendRemote.destination) || chooseCount === 0,
        }}
      >
        <hgroup>
          <h3 style={{ fontSize: 24 }}>{i18n.t('titles.resend_destination')}</h3>
          <p>
            {i18n.t('paragraphs.choose_destination', { chooseCount, joinArrays: ' ' })}
          </p>
        </hgroup>
        <TransferTabs
          type="destination"
          checkList={resendRemote}
          onUpdateRemote={handleUpdateWorklistRemote}
        />
      </Modal>
      {/* Download Modal */}
      <Modal
        {...downloadModal.modalProps}
        okButtonProps={{
          variant: 'outlined',
          color: 'primary',
          disabled: !Object.values(downloadSwitch).includes(true),
        }}
      >
        <p style={{ lineHeight: '2.25rem' }}>{i18n.t('descriptions.download')}</p>
        <TransferSwitchGroup
          checked={downloadSwitch}
          onChange={setDownloadSwitch}
          groupText={{
            rs: `${i18n.t('buttons.download')} ${i18n.t('titles.rs')} `,
            images: `${i18n.t('buttons.download')} ${i18n.t('titles.image')}`,
          }}
          style={{
            padding: 16,
            marginLeft: 0,
            marginTop: 8,
            background: color.gray[4],
          }}
          styles={{
            text: { flex: '0 0 132px' },
          }}
        />
      </Modal>
    </>
  )
}

export default OperationBtn
