import React, { useRef } from 'react'

import { Flex } from 'antd'
import { get, set } from 'lodash-es'
import { CSSTransition } from 'react-transition-group'

import { color } from 'src/styles/utils/variables'
import '../../styles/utils/transition.css'

// Define possible status types
type Status = 'error' | 'warning' | ''

// Updated Props definition
type Props = {
  in: boolean; // renamed from isError
  message?: string;
  timeout?: number;
  children: React.ReactNode;
  status?: Status;
  style?: React.CSSProperties;
  styles?: {
    readonly message?: React.CSSProperties;
  };
  className?: string;
  classNames?: {
    readonly message?: string;
  };
}

const statusColors: Record<string, string> = {}
set(statusColors, 'error', color.error)
set(statusColors, 'warning', color.primary.default)

function FieldExtra({
  in: show, // rename prop to local variable 'show'
  message,
  children,
  timeout = 500,
  status = '',
  style,
  styles,
  className,
  classNames,
}: Props) {
  const nodeRef = useRef<HTMLDivElement>(null)
  const textColor = get(statusColors, status, undefined)

  return (
    <Flex
      vertical
      className={className}
      style={{ width: '100%', position: 'relative', ...style }}
    >
      {children}
      <CSSTransition
        in={show}
        timeout={timeout}
        classNames="collapse-transition"
        unmountOnExit
        nodeRef={nodeRef}
      >
        <div
          className={classNames?.message}
          style={{ position: 'relative', color: textColor }}
        >
          <div ref={nodeRef} style={{ position: 'absolute', ...styles?.message }}>
            {message}
          </div>
        </div>
      </CSSTransition>
    </Flex>
  )
}

export default FieldExtra
