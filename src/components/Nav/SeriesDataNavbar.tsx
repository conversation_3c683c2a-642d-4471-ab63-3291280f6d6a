import { useEffect, useState } from 'react'

import { <PERSON>lex, Tooltip } from 'antd'

import {
  DeleteIcon, PlayIcon, PriorityIcon, StopIcon,
} from 'src/assets/icons'
import useAlert from 'src/hook/useAlert'
import i18n from 'src/i18n'
import {
  usePatchPrioritizeWorklistGroupMutation,
  usePatchWorklistGroupMutation,
} from 'src/services/api'
import { useAppSelector } from 'src/store/hook'
import { WorkStatusEnum } from 'src/utils/enum'

import Button from '../Button'
import { ModalBtn } from '../Modal'

interface Props {
  history?: boolean
}

const navItem = [
  {
    key: 'Series Count',
    content: 'series_count',
  },
  {
    key: 'Study Description',
    content: 'study_description',
  },
  {
    key: 'Last Modified',
    content: 'last_modified',
  },
]

function SeriesDataNavbar({ history }: Props) {
  const [btnGroup, setBtnGroup] = useState<OperationBtnGroup[]>([
    { isDisabled: true },
    { isDisabled: true },
    { isDisabled: true },
  ])
  const { worklistGroupFocus, worklistGroup, worklist } = useAppSelector((state) => state.worklistReducer)
  const worklistGroupData = worklistGroup.find((word: any) => word.id === worklistGroupFocus.id) || undefined
  const [patchPrioritizeWorklistGroupMutation] = usePatchPrioritizeWorklistGroupMutation()
  const [patchWorklistGroupMutation] = usePatchWorklistGroupMutation()
  // hook
  const handleAlert = useAlert()

  const newBtnGroup = (arr: boolean[]) => {
    const newGroup = btnGroup.map((btn, index) => ({
      ...btn, isDisabled: arr[index],
    }))
    setBtnGroup(newGroup)
  }

  const patchPrioritizeWorklistGroup = async () => {
    if (!worklistGroupData) return
    try {
      await patchPrioritizeWorklistGroupMutation({ worklist_group_id: worklistGroupData.id }).unwrap()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const suspendWorklistGroup = async () => {
    if (!worklistGroupData) return
    try {
      await patchWorklistGroupMutation({
        worklist_group_id: worklistGroupData.id,
        status: worklistGroupData.study_status === WorkStatusEnum.SUSPENDED
          ? WorkStatusEnum.PENDING
          : WorkStatusEnum.SUSPENDED,
      }).unwrap()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  const removeWorklistGroup = async () => {
    if (!worklistGroupData) return
    try {
      await patchWorklistGroupMutation(
        {
          worklist_group_id: worklistGroupData.id,
          status: 'REMOVED',
        },
      ).unwrap()
    } catch (e) {
      handleAlert({ content: (e as Err).data?.detail }, 'Msg', 'error')
    }
  }

  useEffect(() => {
    // handle the button able when series change
    if (worklistGroupData) {
      if (worklistGroupData.study_status === WorkStatusEnum.PROCESSING) {
        return newBtnGroup([true, true, true])
      }
      if (worklistGroupData.study_status === WorkStatusEnum.SUSPENDED) {
        return newBtnGroup([true, false, true])
      }
      return newBtnGroup([false, false, false])
    }
    return newBtnGroup([true, true, true])
  }, [worklistGroupData])

  return (
    <nav className="series-navbar">
      {
        navItem.map((item) => (
          <div key={item.key}>
            <h4 className="series-navbar-title">{item.key}</h4>
            <p
              className="series-navbar-content"
              style={{ color: '#fff' }}
            >
              {worklistGroupData ? worklist[item.content as keyof SeriesNavbar] : ''}
            </p>
          </div>
        ))
      }
      {
        !history && (
          <div>
            <h4 className="series-navbar-title">{i18n.t('titles.operation')}</h4>
            <Flex component="nav" gap={12}>
              <ModalBtn
                btnName={(<DeleteIcon />)}
                modalTitle={i18n.t('modal_titles.delete_confirmation')}
                okText={i18n.t('buttons.remove')}
                onOk={removeWorklistGroup}
                width={350}
                modalBodyStyle={{ padding: '4.75rem 0' }}
                btnClass="icon-only"
                btnDisabled={btnGroup[0].isDisabled}
                tooltip={i18n.t('tooltips.worklist_remove')}
              >
                {i18n.t('modal_contents.delete_confirmation', {
                  item: `"${worklistGroupData?.patient_id}"`,
                  joinArrays: ' ',
                })}
              </ModalBtn>
              <Tooltip
                title={
                  btnGroup[0].isDisabled
                    ? i18n.t('tooltips.worklist_start')
                    : i18n.t('tooltips.worklist_suspend')
                }
              >
                <Button
                  disabled={btnGroup[1].isDisabled}
                  onClick={suspendWorklistGroup}
                  className="icon-only"
                >
                  {worklistGroupData?.study_status === WorkStatusEnum.SUSPENDED ? <PlayIcon /> : <StopIcon />}
                </Button>
              </Tooltip>
              <Tooltip title={i18n.t('tooltips.worklist_prioritize')}>
                <Button
                  disabled={btnGroup[2].isDisabled}
                  onClick={patchPrioritizeWorklistGroup}
                  className="icon-only"
                >
                  <PriorityIcon />
                </Button>
              </Tooltip>
            </Flex>
          </div>
        )
      }
    </nav>
  )
}

export default SeriesDataNavbar
