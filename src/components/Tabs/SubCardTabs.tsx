import React from 'react'

import { ConfigProvider, Tabs, type TabsProps } from 'antd'

import { color } from 'src/styles/utils/variables'

type Props = TabsProps & {}

function SubCardTabs({ className, rootClassName, ...tabsProps }: Props) {
  return (
    <ConfigProvider theme={{
      components: {
        Tabs: {
          margin: 0,
          itemColor: color.gray[0].default,
          colorBgContainer: color.gray[3],
          cardBg: color.gray[5],
          colorBorder: 'transparent',
          colorPrimary: 'transparent',
          colorBorderBg: 'transparent',
          colorPrimaryBorder: 'rgba(0,0,0,0)',
          colorBorderSecondary: 'transparent',
        },
      },
    }}
    >
      <Tabs
        type="card"
        className={`${rootClassName} ${className}`}
        {...tabsProps}
      />
    </ConfigProvider>
  )
}

export default SubCardTabs
