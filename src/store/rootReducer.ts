import { combineReducers } from '@reduxjs/toolkit'
import { persistReducer } from 'redux-persist'
import storage from 'redux-persist/es/storage'

// eslint-disable-next-line import/no-cycle
import authReducer from './reducers/authSlice'
import configReducer from './reducers/configSlice'
import detailReducer from './reducers/detailSlice'
import notifySlice from './reducers/notifySlice'
import protocolReducer from './reducers/protocolSlice'
import remoteReducer from './reducers/remoteSlice'
import structureReducer from './reducers/structureSlice'
import websocketReducer from './reducers/websocketSlice'
import worklistReducer from './reducers/worklistSlice'
import { api } from '../services/api'

const authReducerConfig = {
  key: 'authReducer',
  storage,
  whitelist: ['isAuth', 'rememberAccount', 'token', 'username'],
}

const rootReducer = combineReducers({
  [api.reducerPath]: api.reducer,
  authReducer: persistReducer(authReducerConfig, authReducer),
  configReducer,
  detailReducer,
  notifySlice,
  protocolReducer,
  remoteReducer,
  structureReducer,
  websocketReducer,
  worklistReducer,
})

export default rootReducer
