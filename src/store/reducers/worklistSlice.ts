import { createSlice } from '@reduxjs/toolkit'

// eslint-disable-next-line import/no-cycle
import { api } from 'src/services/api'

export interface Worklist {
  worklistGroup: WorklistGroupType[]
  worklistGroupFocus: WorklistGroupFocusType
  worklistGroupPage: WorklistGroupPageType
  worklistGroupSearch: WorklistGroupSearchType
  worklist: WorklistType
  worklistDetail: WorklistDetailType
  updateWorklistDetailCheck: boolean
  resendRemote: Pick<SourceDestinationType, 'destination'>
}

const initialState: Worklist = {
  worklistGroup: [],
  worklistGroupFocus: {
    id: undefined,
    timeStamp: undefined,
  },
  worklistGroupPage: {
    current: 1,
    total: 0,
    size: 0,
  },
  worklistGroupSearch: {
    patient_id: undefined,
    study_status: undefined,
    study_date_range_start: undefined,
    study_date_range_end: undefined,
    order_key: '',
    ascend: false,
  },
  worklist: {
    series_count: 0,
    study_description: '',
    last_modified: '',
    series: [],
  },
  worklistDetail: {
    patient_id: '',
    structure_set_label: '',
    study_date: '',
    study_description: '',
    use_protocol: null,
    protocol_description: '',
    series_time: '',
    series_description: '',
    destination: {
      remote_server: [],
      folder: [],
    },
    structures: [],
    customized_structures: [],
  },
  updateWorklistDetailCheck: false,
  resendRemote: {
    destination: {
      remote_server: [],
      folder: [],
    },
  },
}

export const worklistSlice = createSlice({
  name: 'worklist',
  initialState,
  reducers: {
    resetWorklistState: () => initialState,
    clearWorklist: (state) => {
      state.worklistGroupFocus = initialState.worklistGroupFocus
      state.worklist = initialState.worklist
    },
    clearWorklistDetail: (state) => {
      state.worklistDetail = initialState.worklistDetail
      state.updateWorklistDetailCheck = initialState.updateWorklistDetailCheck
    },
    clearResendRemote: (state) => {
      state.resendRemote = initialState.resendRemote
    },
    updateWorklistFocus: (state, { payload }) => {
      const { id } = payload
      state.worklistGroupFocus.id = id
      state.worklistGroupFocus.timeStamp = Date.now()
    },
    updateCurrentPage: (state, { payload }) => {
      const { page } = payload
      state.worklistGroupPage.current = page
    },
    updateWorklistGroupSearch: (state, { payload }) => {
      state.worklistGroupSearch = { ...state.worklistGroupSearch, ...payload }
    },
    updateWorklistDetail: (state, { payload }) => {
      state.worklistDetail = { ...state.worklistDetail, ...payload }
      state.updateWorklistDetailCheck = true
      state.worklistDetail.use_protocol = null
    },
    updateWorklistProtocol: (state, { payload }) => {
      const { value } = payload
      state.worklistDetail.use_protocol = value
      state.updateWorklistDetailCheck = true
    },
    updateWorklistRemote: (state, { payload }) => {
      const { remote, value } = payload
      state.worklistDetail.destination[remote === 'remote_server' ? 'remote_server' : 'folder'] = value
      state.updateWorklistDetailCheck = true
      state.worklistDetail.use_protocol = null
    },
    updateWorklistStructure: (state, { payload }) => {
      const { id } = payload
      const { structures } = state.worklistDetail
      if (structures.includes(id)) {
        const index = structures.indexOf(id)
        structures.splice(index, 1)
      } else {
        structures.push(id)
      }
      state.updateWorklistDetailCheck = true
      state.worklistDetail.use_protocol = null
    },
    addWorklistCustomizedStructures: (state, { payload }) => {
      const { data } = payload
      const { customized_structures: customizedStructures } = state.worklistDetail
      const filterCustomizedStructures = customizedStructures
        .map((item) => item.id)
        .filter((id): id is number => id !== undefined)
      const maxId = Math.max(...filterCustomizedStructures)

      customizedStructures.push({ ...data, id: maxId === -Infinity ? 0 : maxId + 1 })
      state.updateWorklistDetailCheck = true
      state.worklistDetail.use_protocol = null
    },
    updateWorklistCustomizedStructures: (state, { payload }) => {
      const { data } = payload
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { customized_structures } = state.worklistDetail
      state.worklistDetail.customized_structures = customized_structures.map(
        (item) => (item.id === data.id ? { ...item, ...data } : item),
      )
      state.updateWorklistDetailCheck = true
      state.worklistDetail.use_protocol = null
    },
    deleteWorklistCustomizedStructures: (state, { payload }) => {
      const { data } = payload
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { customized_structures } = state.worklistDetail
      state.worklistDetail.customized_structures = customized_structures.filter((item) => item.id !== data.id)
      state.updateWorklistDetailCheck = true
      state.worklistDetail.use_protocol = null
    },
    updateResendRemote: (state, { payload }) => {
      const { remote, value } = payload
      state.resendRemote.destination[remote === 'remote_server' ? 'remote_server' : 'folder'] = value
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        api.endpoints.getWorklistGroup.matchFulfilled,
        (state, { payload }) => {
          state.worklistGroupPage.current = payload.current_page
          state.worklistGroupPage.size = payload.page_size
          state.worklistGroupPage.total = payload.total_data
          state.worklistGroup = payload.worklist_group
          // Focus first item id in worklist group, if original focus disappears, then find the new focus.
          state.worklistGroupFocus.timeStamp = Date.now()
          if (!payload.worklist_group.find((item) => item.id === state.worklistGroupFocus.id)) {
            state.worklistGroupFocus.id = payload.worklist_group.length ? payload.worklist_group[0].id : undefined
          }
        },
      )
      .addMatcher(
        api.endpoints.reGetWorklistGroup.matchFulfilled,
        (state, { payload }) => {
          state.worklistGroupPage.current = payload.current_page
          state.worklistGroupPage.size = payload.page_size
          state.worklistGroupPage.total = payload.total_data
          state.worklistGroup = payload.worklist_group
          // Focus first item id in worklist group, if original focus disappears, then find the new focus.
          state.worklistGroupFocus.timeStamp = Date.now()
          if (!payload.worklist_group.find((item) => item.id === state.worklistGroupFocus.id)) {
            state.worklistGroupFocus.id = payload.worklist_group.length ? payload.worklist_group[0].id : undefined
          }
        },
      )
      .addMatcher(
        api.endpoints.getWorklist.matchFulfilled,
        (state, { payload }) => {
          state.worklist.series_count = payload.series_count
          state.worklist.study_description = payload.study_description
          state.worklist.last_modified = payload.last_modified
          state.worklist.series = payload.series
        },
      )
      .addMatcher(
        api.endpoints.reGetWorklist.matchFulfilled,
        (state, { payload }) => {
          state.worklist.series_count = payload.series_count
          state.worklist.study_description = payload.study_description
          state.worklist.last_modified = payload.last_modified
          state.worklist.series = payload.series
        },
      )
      .addMatcher(
        api.endpoints.getWorklistDetail.matchFulfilled,
        (state, { payload }) => {
          state.worklistDetail = {
            ...payload,
            customized_structures: payload.customized_structures.map((item, index) => ({ ...item, id: index })),
          }
          state.updateWorklistDetailCheck = initialState.updateWorklistDetailCheck
        },
      )
      .addMatcher(
        api.endpoints.getWorklistProtocolDetail.matchFulfilled,
        (state, { payload }) => {
          state.worklistDetail.structure_set_label = payload.structure_set_label
          state.worklistDetail.protocol_description = payload.description
          state.worklistDetail.destination = payload.destination
          state.worklistDetail.structures = payload.structures
          state.worklistDetail.customized_structures = payload.customized_structures.map(
            (item, index) => ({ ...item, id: index }),
          )
        },
      )
  },
})

export const {
  resetWorklistState,
  clearWorklist,
  clearWorklistDetail,
  clearResendRemote,
  updateWorklistFocus,
  updateCurrentPage,
  updateWorklistGroupSearch,
  updateWorklistDetail,
  updateWorklistProtocol,
  updateWorklistRemote,
  updateWorklistStructure,
  addWorklistCustomizedStructures,
  updateWorklistCustomizedStructures,
  deleteWorklistCustomizedStructures,
  updateResendRemote,
} = worklistSlice.actions
export default worklistSlice.reducer
