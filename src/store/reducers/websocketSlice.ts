import { createSlice } from '@reduxjs/toolkit'

export interface WebSocketState {
  message: {
    content?: 'worklist_update' | 'notify_update' | 'remote_update',
    timestamp?: number
  }
}

const initialState: WebSocketState = {
  message: {
    content: undefined,
    timestamp: undefined,
  },
}

export const websocketSlice = createSlice({
  name: 'websocket',
  initialState,
  reducers: {
    setMessage: (state, action) => {
      state.message = {
        content: action.payload,
        timestamp: Date.now(),
      }
    },
  },
})

export const { setMessage } = websocketSlice.actions
export default websocketSlice.reducer
