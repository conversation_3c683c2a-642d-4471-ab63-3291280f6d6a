import { useEffect } from 'react'

import {
  createBrowserRouter,
  createRoutesFromElements,
  Navigate,
  Route,
  RouterProvider,
} from 'react-router'

import useParseManual from './hook/useParseManual'
import NavigationLayout from './Layout/NavigationLayout'
import ConfigSettingLayout from './pages/ConfigSettingLayout'
import HistoryPage from './pages/history-page'
import Login from './pages/Login'
import ManualPage from './pages/ManualPage'
import Directions from './pages/ManualPage/Directions'
import FAQ from './pages/ManualPage/FAQ'
import NewCreateProtocolPage from './pages/NewConfigSettingPages/CreateProtocolPage.new'
import NewProtocolConfigPage from './pages/NewConfigSettingPages/ProtocolConfigPage.new'
import WorklistSetting from './pages/NewConfigSettingPages/WorklistSetting'
import { Protocol, RemotePage } from './pages/SettingPages'
import OldCreateProtocolPage from './pages/SettingPages/CreateProtocolPage.old'
import OldProtocolConfigPage from './pages/SettingPages/ProtocolConfigPage.old'
import Structure from './pages/SettingPages/Structure'
import TaskPage from './pages/task-page'
import { useAppSelector } from './store/hook/index'

const { newConfigSetting } = window.config.env

const ProtocolConfigPage = {
  New: NewProtocolConfigPage,
  Old: OldProtocolConfigPage,
}

const CreateProtocolPage = {
  New: NewCreateProtocolPage,
  Old: OldCreateProtocolPage,
}

function App() {
  const { isAuth } = useAppSelector((state) => state.authReducer)
  const [jsonData, menu] = useParseManual()

  useEffect(() => {
    const loadingEl = document.getElementById('root-rending')
    if (loadingEl) {
      loadingEl.classList.add('fade-out')

      const removeTimer = setTimeout(() => {
        loadingEl.remove()
      }, 1000) // 與 .fadeOut 2s 相同

      return () => clearTimeout(removeTimer)
    }

    return () => { }
  }, [])

  const router = createBrowserRouter(
    createRoutesFromElements(
      !isAuth
        ? (
          <>
            <Route path="/" element={<Login />} />
            <Route path="*" element={<Navigate to="/" />} />
          </>
        ) : (
          <Route path="/" element={<NavigationLayout />}>
            <Route index element={(<TaskPage />)} />
            <Route
              path="worklist/:id"
              element={newConfigSetting ? <WorklistSetting /> : <ConfigSettingLayout navigatePage="/" />}
            />
            <Route path="history" element={(<HistoryPage />)} />
            <Route
              path="history/:id"
              element={newConfigSetting
                ? <WorklistSetting isHistory />
                : <ConfigSettingLayout draw navigatePage="/history" />}
            />
            <Route path="protocols" element={<Protocol />} />
            <Route
              path="protocols/:id"
              element={newConfigSetting ? <ProtocolConfigPage.New /> : <ProtocolConfigPage.Old />}
            />
            <Route
              path="protocols/create"
              element={newConfigSetting ? <CreateProtocolPage.New /> : <CreateProtocolPage.Old />}
            />
            <Route path="structure" element={<Structure />} />
            <Route path="source" element={<RemotePage />} />
            <Route path="destination" element={<RemotePage />} />
            <Route path="Manual" element={<ManualPage menu={menu} />}>
              <Route index element={<FAQ />} />
              <Route
                path="directions"
                element={(
                  <article className="manual-directions-article">
                    <Directions data={jsonData} />
                  </article>
                )}
              />
            </Route>
            <Route
              path="*"
              element={(
                <section style={{
                  display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh',
                }}
                >
                  <hgroup className="circle-group">
                    <h2>404</h2>
                    <p>Page Not Found!</p>
                  </hgroup>
                </section>
              )}
            />
          </Route>
        ),
    ),
  )

  return (
    <RouterProvider router={router} />
  )
}

export default App
