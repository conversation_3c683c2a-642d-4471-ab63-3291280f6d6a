// utils/buildRowData.ts
import React from 'react'

import { Image, Flex } from 'antd'
import groupBy from 'lodash-es/groupBy'
import isNil from 'lodash-es/isNil'
import map from 'lodash-es/map'
import values from 'lodash-es/values'

import AboutDsecriptionList from 'src/components/List/AboutDsecriptionList'
import { color } from 'src/styles/utils/variables'

/**
 * Render a single item based on its type.
 * For 'list' type, push the item into listItems array and return a placeholder.
 */
function renderItem(
  item: AboutList,
  listItems: AboutList[],
): React.ReactNode | 'list' {
  switch (item.type) {
    case 'image':
      return (
        <Image
          key={item?.Title || item.Description}
          src={item.Description}
          preview={false}
          style={{ width: '100%' }}
        />
      )
    case 'head':
      return <h3 key={item?.Title}>{item.Title}</h3>
    case 'text':
      return (
        <Flex key={item?.Title} vertical>
          <h4
            style={{
              marginBottom: 0,
              color: color.gray[0].default,
              fontWeight: 900,
            }}
          >
            {item?.Title}
          </h4>
          <p key={item?.Description}>{item.Description}</p>
        </Flex>
      )
    case 'list':
      listItems.push(item)
      return 'list'
    default:
      return null
  }
}

// 將第一次出現的「list」佔位符替換為 AboutDsecriptionList 元件，並刪除後續佔位符。

const applyListReplacement = (
  nodes: (React.ReactNode | 'list')[],
  listItems: AboutList[],
  listKey: string,
): React.ReactNode[] => {
  let replaced = true
  return nodes.reduce<React.ReactNode[]>((acc, node) => {
    if (node === 'list' && replaced) {
      replaced = false
      acc.push(
        <AboutDsecriptionList key={listKey} dataSource={listItems} />,
      )
    } else if (!isNil(node) && node !== 'list') {
      acc.push(node)
    }
    return acc
  }, [])
}

// 處理一組相同 Row 的 AboutList 項。
const processGroup = (
  group: AboutList[],
  groupIndex: number,
): { key: number; children: React.ReactNode[] } => {
  const listItems: AboutList[] = []
  const renderedNodes = group.map((item) => renderItem(item, listItems))
  const children = applyListReplacement(renderedNodes, listItems, `list-${groupIndex}`)
  return { key: groupIndex, children }
}

export const buildRowData = (container: AboutList[]): { key: number; children: React.ReactNode[] }[] => {
  const grouped = groupBy(container, 'Row')
  const groupsArray = values(grouped)
  return map(groupsArray, (group, index) => processGroup(group, index))
}
