import {
  capitalize, isNil, omit, uniq,
} from 'lodash-es'

import i18n from 'src/i18n'
import { WorkStatusEnum } from 'src/utils/enum'

const { newConfigSetting } = window.config.env

export const withoutTime = (date: string) => (date.split(' ')[0])

export const withoutSecond = (date: string) => (date.replace(/:\d{2}$/, ''))

export const sortSpecificCase = (a: string, b: string, statusOrder: string[]) => (
  statusOrder.indexOf(a) - statusOrder.indexOf(b)
)

export const removeId = (list: any) => list.map((item: any) => omit(item, 'id'))

export const sortDate = ((a: string, b: string) => {
  const dateA = new Date(a)
  const dateB = new Date(b)
  if (dateA < dateB) return -1
  if (dateA > dateB) return 1
  return 0 // 日期相等
})

export const formattedStatus = (text: string) => (text === WorkStatusEnum.NO_MATCH ? 'No Match' : capitalize(text))

export const checkDicomTagConform = (value: string | number): boolean => {
  const dicomTagFormat = /^\([a-zA-Z0-9]{4},[a-zA-Z0-9]{4}\)$/
  return !dicomTagFormat.test(value.toString())
}

const mapErrorKeyToMessage = (key: string): string => {
  switch (key) {
    case 'customized_structures':
      return i18n.t('titles.structures')
    case 'source':
      return i18n.t('titles.source_destination')
    default:
      return i18n.t(`titles.${key}`)
  }
}

export type GenerateErrorMessageParams = (
  [string, boolean]
  | ['customized_structures', CustomizedStructuresType[]])

export const generateErrorMessage = (list: GenerateErrorMessageParams[]): string => {
  const errorList = list
    .filter(([, value]) => (Array.isArray(value) ? value.length : value))
    .map(([key]) => key)

  if (errorList.length === 1 && errorList[0] === 'customized_structures') {
    const errorName = newConfigSetting
      ? i18n.t('buttons.process_structures') : i18n.t('titles.add_customized_structures')
    return i18n.t(
      'modal_contents.wrong_with_customized_structures',
      { item: `"${errorName}"`, joinArrays: ' ' },
    )
  }

  const uniqueErrors = uniq(errorList.map(mapErrorKeyToMessage))

  if (uniqueErrors.length > 1) {
    const errorNameList = uniqueErrors.map((str) => `"${str}"`).join(', ')
    return i18n.t('modal_contents.multiple_items_required', { item: errorNameList, joinArrays: ' ' })
  }
  return i18n.t('modal_contents.item_required', { item: `"${uniqueErrors[0]}"`, joinArrays: ' ' })
}

export const compareSort = (a: unknown, b: unknown): number => {
  if (isNil(a) && !isNil(b)) return -1
  if (isNil(b) && !isNil(a)) return 1
  if (isNil(a) && isNil(b)) return 0
  return (a as number) - (b as number)
}

type ItemType<T> = T extends { id?: string, name?: string } ? T : { id?: string | number, name?: string }
export const compareIdName = <T, K>(a: ItemType<T>, b: ItemType<K>) => `${a.id}-${a.name}` === `${b.id}-${b.name}`
