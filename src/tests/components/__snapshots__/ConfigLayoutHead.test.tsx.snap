// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[` ConfigLayoutHead.tsx  > no navigatePage 1`] = `
<div>
  <header
    class="ant-layout-header head undefined css-dev-only-do-not-override-cdzvx5"
    style="padding: 0px;"
  >
    <nav
      style="position: relative; z-index: 200;"
    >
      <h1>
        <button
          class="btn icon-only"
          type="button"
        >
          <svg
            fill="none"
            height="24"
            viewBox="0 0 16 16"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g
              id="chevrons-left"
            >
              <path
                d="M9.33334 11.3333L6 7.99997L9.33334 4.66663"
                id="Vector"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.33334"
              />
            </g>
          </svg>
        </button>
        Test_001
      </h1>
      <ul
        class="head-navbar"
      >
        <li
          style="padding: 0px;"
        >
          <button
            class="ant-dropdown-trigger"
            type="button"
          >
            <svg
              fill="none"
              height="30"
              viewBox="0 0 30 30"
              width="30"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle
                cx="15"
                cy="15"
                r="14.5"
                stroke="white"
              />
              <path
                d="M21.2223 20.9999V19.4721C21.2223 18.6617 20.9003 17.8845 20.3273 17.3115C19.7543 16.7384 18.9771 16.4165 18.1667 16.4165H12.0556C11.2452 16.4165 10.468 16.7384 9.89495 17.3115C9.32192 17.8845 9 18.6617 9 19.4721V20.9999"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.35"
              />
              <path
                d="M15.1112 13.3611C16.7988 13.3611 18.1668 11.9931 18.1668 10.3056C18.1668 8.61802 16.7988 7.25 15.1112 7.25C13.4237 7.25 12.0557 8.61802 12.0557 10.3056C12.0557 11.9931 13.4237 13.3611 15.1112 13.3611Z"
                stroke="white"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.35"
              />
            </svg>
          </button>
        </li>
        <li>
          <button
            class="btn notice-btn btn-link"
            type="button"
          >
            <span
              class="ant-badge css-dev-only-do-not-override-cdzvx5"
            >
              <svg
                fill="none"
                height="22"
                viewBox="0 0 22 22"
                width="22"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g
                  id="bell"
                >
                  <path
                    d="M16.5 7.33325C16.5 5.87456 15.9205 4.47561 14.8891 3.44416C13.8576 2.41271 12.4587 1.83325 11 1.83325C9.54131 1.83325 8.14236 2.41271 7.11091 3.44416C6.07946 4.47561 5.5 5.87456 5.5 7.33325C5.5 13.7499 2.75 15.5833 2.75 15.5833H19.25C19.25 15.5833 16.5 13.7499 16.5 7.33325Z"
                    id="Vector"
                    stroke="white"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                  />
                  <path
                    d="M12.5857 19.25C12.4246 19.5278 12.1933 19.7584 11.9149 19.9187C11.6366 20.079 11.3211 20.1634 10.9999 20.1634C10.6787 20.1634 10.3632 20.079 10.0849 19.9187C9.80654 19.7584 9.57522 19.5278 9.41406 19.25"
                    id="Vector_2"
                    stroke="white"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                  />
                </g>
              </svg>
            </span>
          </button>
        </li>
      </ul>
    </nav>
  </header>
</div>
`;
