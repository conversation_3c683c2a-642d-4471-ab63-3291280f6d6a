// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`SeriesDataNavbar.tsx > WorklistGroup PROCESSING 1`] = `
<div>
  <nav
    class="series-navbar"
  >
    <div>
      <h4
        class="series-navbar-title"
      >
        Series Count
      </h4>
      <p
        class="series-navbar-content"
        style="color: rgb(255, 255, 255);"
      >
        1
      </p>
    </div>
    <div>
      <h4
        class="series-navbar-title"
      >
        Study Description
      </h4>
      <p
        class="series-navbar-content"
        style="color: rgb(255, 255, 255);"
      >
        23
      </p>
    </div>
    <div>
      <h4
        class="series-navbar-title"
      >
        Last Modified
      </h4>
      <p
        class="series-navbar-content"
        style="color: rgb(255, 255, 255);"
      >
        2023-10-18
      </p>
    </div>
    <div>
      <h4
        class="series-navbar-title"
      >
        Operation
      </h4>
      <nav
        class="ant-flex css-dev-only-do-not-override-cdzvx5"
        style="gap: 12px;"
      >
        <button
          class="btn outline icon-only"
          disabled=""
        >
          <svg
            fill="none"
            height="16"
            viewBox="0 0 16 16"
            width="16"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g
              id="trash-2"
            >
              <path
                d="M2 4H3.33333H14"
                id="Vector"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M12.6668 3.99992V13.3333C12.6668 13.6869 12.5264 14.026 12.2763 14.2761C12.0263 14.5261 11.6871 14.6666 11.3335 14.6666H4.66683C4.31321 14.6666 3.97407 14.5261 3.72402 14.2761C3.47397 14.026 3.3335 13.6869 3.3335 13.3333V3.99992M5.3335 3.99992V2.66659C5.3335 2.31296 5.47397 1.97382 5.72402 1.72378C5.97407 1.47373 6.31321 1.33325 6.66683 1.33325H9.3335C9.68712 1.33325 10.0263 1.47373 10.2763 1.72378C10.5264 1.97382 10.6668 2.31296 10.6668 2.66659V3.99992"
                id="Vector_2"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M6.6665 7.33325V11.3333"
                id="Vector_3"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M9.3335 7.33325V11.3333"
                id="Vector_4"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </g>
          </svg>
        </button>
        <button
          class="btn icon-only"
          disabled=""
          type="button"
        >
          <svg
            fill="none"
            height="18"
            viewBox="0 0 18 18"
            width="18"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g
              id="pause"
            >
              <path
                d="M7.5 3H4.5V15H7.5V3Z"
                id="Vector"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M13.5 3H10.5V15H13.5V3Z"
                id="Vector_2"
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </g>
          </svg>
        </button>
        <button
          class="btn icon-only"
          disabled=""
          type="button"
        >
          <svg
            fill="none"
            height="30"
            viewBox="0 0 30 30"
            width="30"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g
              id="priority"
            >
              <path
                d="M14.5716 9.28571H24.2859C24.4374 9.28571 24.1787 9.22551 24.2859 9.11835C24.393 9.01118 24.2859 8.86584 24.2859 8.71429V7.57143C24.2859 7.41988 24.393 7.27453 24.2859 7.16737C24.1787 7.0602 24.4374 7 24.2859 7H14.5716C14.42 7 14.2747 7.0602 14.1675 7.16737C14.0604 7.27453 14.0002 7.41988 14.0002 7.57143V8.71429C14.0002 8.86584 14.0604 9.01118 14.1675 9.11835C14.2747 9.22551 14.42 9.28571 14.5716 9.28571ZM14.5716 13.8571H24.2859C24.4374 13.8571 24.1787 13.7969 24.2859 13.6898C24.393 13.5826 24.2859 13.4373 24.2859 13.2857V12.1429C24.2859 11.9913 24.393 11.846 24.2859 11.7388C24.1787 11.6316 24.4374 11.5714 24.2859 11.5714H14.5716C14.42 11.5714 14.2747 11.6316 14.1675 11.7388C14.0604 11.846 14.0002 11.9913 14.0002 12.1429V13.2857C14.0002 13.4373 14.0604 13.5826 14.1675 13.6898C14.2747 13.7969 14.42 13.8571 14.5716 13.8571ZM24.2859 20.7143H14.5716C14.42 20.7143 14.2747 20.7745 14.1675 20.8817C14.0604 20.9888 14.0002 21.1342 14.0002 21.2857V22.4286C14.0002 22.5801 14.0604 22.7255 14.1675 22.8326C14.2747 22.9398 14.42 23 14.5716 23H24.2859C24.4374 23 24.1787 22.9398 24.2859 22.8326C24.393 22.7255 24.2859 22.5801 24.2859 22.4286V21.2857C24.2859 21.1342 24.393 20.9888 24.2859 20.8817C24.1787 20.7745 24.4374 20.7143 24.2859 20.7143ZM14.5716 18.4286H24.2859C24.4374 18.4286 24.1787 18.3684 24.2859 18.2612C24.393 18.154 24.2859 18.0087 24.2859 17.8571V16.7143C24.2859 16.5627 24.393 16.4174 24.2859 16.3102C24.1787 16.2031 24.4374 16.1429 24.2859 16.1429H14.5716C14.42 16.1429 14.2747 16.2031 14.1675 16.3102C14.0604 16.4174 14.0002 16.5627 14.0002 16.7143V17.8571C14.0002 18.0087 14.0604 18.154 14.1675 18.2612C14.2747 18.3684 14.42 18.4286 14.5716 18.4286ZM6.57159 11.5714H8.28587V22.4286C8.28587 22.5801 8.34608 22.7255 8.45324 22.8326C8.5604 22.9398 8.70575 23 8.8573 23H10.0002C10.1517 23 10.2971 22.9398 10.4042 22.8326C10.5114 22.7255 10.5716 22.5801 10.5716 22.4286V11.5714H12.2859C12.7934 11.5714 13.0498 10.9557 12.6898 10.5961L9.83266 7.1675C9.72551 7.06042 9.58022 7.00026 9.42873 7.00026C9.27724 7.00026 9.13195 7.06042 9.0248 7.1675L6.16766 10.5961C5.80909 10.955 6.06373 11.5714 6.57159 11.5714Z"
                fill="currentColor"
                id="Vector"
              />
            </g>
          </svg>
        </button>
      </nav>
    </div>
  </nav>
</div>
`;

exports[`SeriesDataNavbar.tsx > history page no have operation 1`] = `
<div>
  <nav
    class="series-navbar"
  >
    <div>
      <h4
        class="series-navbar-title"
      >
        Series Count
      </h4>
      <p
        class="series-navbar-content"
        style="color: rgb(153, 153, 153);"
      >
        0
      </p>
    </div>
    <div>
      <h4
        class="series-navbar-title"
      >
        Study Description
      </h4>
      <p
        class="series-navbar-content"
        style="color: rgb(255, 255, 255);"
      />
    </div>
    <div>
      <h4
        class="series-navbar-title"
      >
        Last Modified
      </h4>
      <p
        class="series-navbar-content"
        style="color: rgb(255, 255, 255);"
      />
    </div>
  </nav>
</div>
`;
