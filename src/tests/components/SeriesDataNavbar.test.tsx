import { waitFor, waitForElementToBeRemoved } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import SeriesDataNavbar from 'src/components/Nav/SeriesDataNavbar'
import { processingWorklistGroup, worklistReducer } from 'src/tests/mock/worklistData'
import renderWithProviders from 'src/tests/utils/renderWithProviders'

describe('SeriesDataNavbar.tsx', () => {
  const user = userEvent.setup()

  it('history page no have operation', () => {
    const { container, queryAllByRole } = renderWithProviders(<SeriesDataNavbar history />)
    const operations = queryAllByRole('button')

    expect(operations).toHaveLength(0)
    expect(container).toMatchSnapshot()
  })

  it('WorklistGroup PROCESSING', async () => {
    const { container, getAllByRole } = renderWithProviders(<SeriesDataNavbar />, {
      preloadedState: {
        worklistReducer: { ...worklistReducer, worklistGroup: processingWorklistGroup },
      },
    })
    expect(getAllByRole('button')).toHaveLength(3)

    await waitFor(() => {
      expect(getAllByRole('button')[1]).toBeDisabled()
      expect(getAllByRole('button')[0]).toBeDisabled()
      expect(getAllByRole('button')[2]).toBeDisabled()
    })
    expect(container).toMatchSnapshot()
  })

  it('WorklistGroup Suspended', async () => {
    const { getAllByRole, getByRole, queryByRole } = renderWithProviders(<SeriesDataNavbar />, {
      preloadedState: {
        worklistReducer,
      },
    })

    // pause and start inference
    await waitFor(() => {
      // Suspended
      expect(getAllByRole('button')[1]).toBeEnabled()
      expect(getAllByRole('button')[0]).toBeDisabled()
      expect(getAllByRole('button')[2]).toBeDisabled()
    })
    user.click(getAllByRole('button')[1])
    // pending
    await waitFor(() => {
      expect(getAllByRole('button')[1]).toBeEnabled()
      expect(getAllByRole('button')[0]).toBeEnabled()
      expect(getAllByRole('button')[2]).toBeEnabled()
    }, { timeout: 5000 })

    // delete and show Confirm  modal
    await user.click(getAllByRole('button')[0])
    expect(queryByRole('dialog')).toBeInTheDocument()

    await user.click(getByRole('button', { name: /remove/i }))
    await waitForElementToBeRemoved(() => getByRole('dialog'))
  })
})
