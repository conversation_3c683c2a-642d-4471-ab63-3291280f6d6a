import {
  fireEvent, screen, waitFor, waitForElementToBeRemoved,
} from '@testing-library/react'

import OperationBtn from 'src/components/Button/OperationBtn'
import renderWithProviders from 'src/tests/utils/renderWithProviders'

describe('operation button group', () => {
  it('Resend Destination Setting modal', async () => {
    // open send modal
    const {
      getAllByRole, getByRole, findByText,
    } = renderWithProviders(<OperationBtn worklistId={1} />)

    fireEvent.click(getAllByRole('button')[2])

    // checkbox
    const checkbox = await screen.findByText(/test-2/g)
    fireEvent.click(checkbox)

    // error alert
    const okButton = screen.getByRole('button', { name: /send/i })
    fireEvent.click(okButton)
    screen.debug(getAllByRole('dialog'))
    expect(await findByText('RS dicom not found.')).toBeInTheDocument()

    // close resend error alert
    fireEvent.click(getByRole('button', { name: /ok/i }))
    await waitFor(() => {
      fireEvent.click(getByRole('button', { name: /cancel/i }))
    })
  })

  it('downloadData', async () => {
    const {
      container, getByRole, queryByRole, getAllByRole, findByRole,
    } = renderWithProviders(<OperationBtn worklistId={1} />)

    const downLoadbutton = getAllByRole('button')[3]
    fireEvent.click(downLoadbutton)

    // error alert
    expect(await findByRole('dialog')).toBeInTheDocument()
    const okButton = getByRole('button', {
      name: /ok/i,
    })

    // close error alert
    fireEvent.click(okButton)
    await waitForElementToBeRemoved(getByRole('dialog'))
    expect(queryByRole('dialog')).not.toBeInTheDocument()
    expect(container).toMatchSnapshot()
  })

  it('disabled', () => {
    // Arrange
    const { container } = renderWithProviders(<OperationBtn />)
    const buttons = screen.getAllByRole('button')

    // Act
    fireEvent.click(buttons[1])
    fireEvent.click(buttons[2])

    // Assert
    expect(buttons[1]).toBeDisabled()
    expect(buttons[2]).toBeDisabled()
    expect(container).toMatchSnapshot()
  })
})
