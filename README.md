# RT Web Frontend

## Requirements 要求

- NodeJS 18+ ：Vite@4.4 requires Node.js version 16+.
- chrome:  Ant Design 5.x 支援最近 2 個版本的現代瀏覽器。預設情況下，使用了一些現代 CSS 特性來提高樣式的可維護性和可擴展性，這些特性在舊版瀏覽器中可能不被支持。


| 特性                                                                                    | antd 版本 | 相容性                                           | 最低Chrome 版本 | 降級相容方案                                                          |
| --------------------------------------------------------------------------------------- | --------- | ------------------------------------------------ | --------------- | --------------------------------------------------------------------- |
| [:where 選擇器](https://developer.mozilla.org/en-US/docs/Web/CSS/:where)                | `>=5.0.0` | [caniuse](https://caniuse.com/?search=%3Awhere)  | Chrome 88       | `<StyleProvider hashPriority="high">`                                 |
| [CSS 邏輯屬性](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties) | `>=5.0.0` | [caniuse](https://caniuse.com/css-logical-props) | Chrome 89       | `<StyleProvider transformers={[legacyLogicalPropertiesTransformer]}>` |

## Test the Web

- https://dev.rt.efai.tw/
- TypeScript - A typed superset of JavaScript designed with large scale applications in mind

## Scripts

| Script       | Description                                             |
| ------------ | ------------------------------------------------------- |
| npm install  | install all dependencies                                |
| npm run dev  | Runs the app in the development mode.                   |
| npm run test | Launches the test runner in the interactive watch mode. |

Open `http://localhost:8080` to view it in the browser.

## Installed Packages

### Base

- TypeScript 
- Vite 
- React 

### Routing 

- react-router

### Linting & Formatting

- ESlint
  - *eslint-config-airbnb
  - *eslint-plugin-import
  - *eslint-plugin-react
  - *eslint-plugin-react-hooks
  - *eslint-plugin-react-refresh
  - *eslint-import-resolver-alias
  - *eslint-import-resolver-typescript

### State Management

- redux
  - Redux Toolkit
  - RTK Query

### UI Component

- Ant Design

### Testing

- Vitest
- React Testing Library
- jsdom
- Mock Service Worker
